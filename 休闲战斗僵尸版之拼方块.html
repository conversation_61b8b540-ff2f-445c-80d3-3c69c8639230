<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>霰弹枪零件升级战斗</title>
    <script src="https://cdn.jsdelivr.net/npm/phaser@3.80.1/dist/phaser.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background: linear-gradient(135deg, #1e3c72, #2a5298);
            overflow: hidden;
            font-family: Arial, sans-serif;
        }
        canvas {
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            height: 100vh;
        }
    </style>
</head>
<body>

<script>
    // 游戏变量
    let game;
    let player, playerWeapon, monsters = [];
    let playerHealth = 500, maxPlayerHealth = 500;
    let currentLevel = 1, currentWave = 1;
    let gameState = 'preparing'; // 'preparing' 准备阶段, 'fighting' 战斗阶段
    let monstersKilled = 0;
    let totalMonstersInWave = 8;
    let isCreatingWave = false; // 防止重复创建波次的标志位
    let battleButton = null; // 战斗按钮

    // 方块消除系统变量
    let grid = [];
    let gridSize = 8;
    let cellSize = 60; // 进一步增大方块尺寸
    let gridOffsetX = 135; // 居中显示 (750 - 8*60) / 2
    let gridOffsetY = 700;
    let currentBlocks = [];
    let draggedBlock = null;
    let blockPreviewY = 1200;

    // 三种颜色的方块收集数量
    let redBlocks = 0;
    let blueBlocks = 0;
    let greenBlocks = 0;
    let redBlocksText = null;
    let blueBlocksText = null;
    let greenBlocksText = null;

    let flyingBlocks = []; // 飞向主角的方块数组
    let blockGridSprites = []; // 存储网格中的方块精灵

    // 方块颜色定义
    const blockColors = [
        { name: 'red', color: 0xe74c3c, textColor: '#e74c3c' },
        { name: 'blue', color: 0x3498db, textColor: '#3498db' },
        { name: 'green', color: 0x27ae60, textColor: '#27ae60' }
    ];

    // 方块形状定义
    const blockShapes = [
        // 单个方块
        [[1]],
        // 直线方块
        [[1, 1]],
        [[1, 1, 1]],
        [[1, 1, 1, 1]],
        [[1], [1]],
        [[1], [1], [1]],
        [[1], [1], [1], [1]],
        // L形方块
        [[1, 0], [1, 1]],
        [[1, 1], [1, 0]],
        [[0, 1], [1, 1]],
        [[1, 0, 0], [1, 1, 1]],
        [[0, 0, 1], [1, 1, 1]],
        [[1, 1, 1], [1, 0, 0]],
        [[1, 1, 1], [0, 0, 1]],
        // 正方形方块
        [[1, 1], [1, 1]],
        [[1, 1, 1], [1, 1, 1], [1, 1, 1]],
        // T形方块
        [[1, 1, 1], [0, 1, 0]],
        [[0, 1], [1, 1], [0, 1]],
        [[0, 1, 0], [1, 1, 1]],
        [[1, 0], [1, 1], [1, 0]],
        // Z形方块
        [[1, 1, 0], [0, 1, 1]],
        [[0, 1, 1], [1, 1, 0]],
        [[1, 0], [1, 1], [0, 1]],
        [[0, 1], [1, 1], [1, 0]]
    ];

    // 玩家属性
    let playerStats = {
        attackDamage: 15,
        attackSpeed: 600,
        maxHealth: 100,
        critChance: 0,
        lifeSteal: 0,
        multiShot: 1
    };



    // 存储所有玩家武器的数组
    let playerWeapons = [];

    // 游戏配置 - 竖屏优化
    const config = {
        type: Phaser.AUTO,
        width: 750,
        height: 1400, // 适合方块游戏的高度
        backgroundColor: '#1a1a2e',
        parent: 'game-container',
        scene: {
            preload: preload,
            create: create,
            update: update
        }
    };

    // 预加载函数
    function preload() {
        // 加载背景图片
        this.load.image('background', 'images/rpg/background8.png');

        // 加载主角图片
        this.load.image('player', 'images/rpg/Character3.png');

        // 加载15个怪物图片
        for (let i = 1; i <= 15; i++) {
            this.load.image(`monster_${i}`, `images/rpg/js (${i}).png`);
        }

        // 加载武器图片
        this.load.image('knife', 'images/knife/kong.png');

        // 加载霰弹枪零件图片
        this.load.image('gun_stock', 'images/gun/枪托.png');
        this.load.image('gun_magazine', 'images/gun/弹夹.png');
        this.load.image('gun_barrel', 'images/gun/枪管.png');

        // 加载子弹图片
        this.load.image('ice_bullet', 'images/gun/冰冻子弹.png');
        this.load.image('fire_bullet', 'images/gun/火焰子弹.png');
        this.load.image('explosive_bullet', 'images/gun/爆炸子弹.png');
    }

    // 创建游戏场景
    function create() {
        // 添加背景图片
        const background = this.add.image(375, 700, 'background');
        background.setDisplaySize(750, 1400); // 适配屏幕大小

        // 创建主角 - 左边位置，向下移动
        player = this.add.image(150, 500, 'player');
        player.setScale(1);
        player.setOrigin(0.5, 1); // 设置旋转中心在底部
        player.setDepth(100 + player.y * 0.1);

        // 创建武器 - 位置在角色右中，与角色重叠
        playerWeapon = this.add.image(player.x + 20, player.y - 30, 'knife');
        playerWeapon.setScale(0.8);
        playerWeapon.setOrigin(0.5, 1);
        playerWeapon.setDepth(100 + player.y * 0.1 + 1);

        // 创建UI
        createUI.call(this);

        // 创建方块游戏网格
        createBlockGrid.call(this);

        // 创建方块纹理
        createBlockTextures.call(this);

        // 生成初始方块
        generateNewBlocks.call(this);

        // 设置方块游戏输入
        setupBlockInput.call(this);

        // 创建三种颜色方块的计数显示
        redBlocksText = this.add.text(200, 90, `红色: ${redBlocks}`, {
            fontSize: '16px',
            fill: '#e74c3c',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5);

        blueBlocksText = this.add.text(375, 90, `蓝色: ${blueBlocks}`, {
            fontSize: '16px',
            fill: '#3498db',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5);

        greenBlocksText = this.add.text(550, 90, `绿色: ${greenBlocks}`, {
            fontSize: '16px',
            fill: '#27ae60',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5);

        // 创建战斗按钮
        battleButton = this.add.text(375, 120, '开始战斗', {
            fontSize: '20px',
            fill: '#ffffff',
            fontFamily: 'Arial',
            fontWeight: 'bold',
            backgroundColor: '#e74c3c',
            padding: { x: 20, y: 10 }
        }).setOrigin(0.5);

        battleButton.setInteractive();
        battleButton.on('pointerdown', () => {
            startBattle.call(this);
        });

        battleButton.on('pointerover', () => {
            battleButton.setScale(1.1);
        });

        battleButton.on('pointerout', () => {
            battleButton.setScale(1);
        });

        // 添加方块游戏说明
        this.add.text(375, 650, '拼方块游戏 - 消除整行获得方块奖励', {
            fontSize: '16px',
            fill: '#ffffff',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5);

        // 添加方块预览区域说明
        this.add.text(375, 1150, '拖拽方块到网格中', {
            fontSize: '14px',
            fill: '#bdc3c7',
            fontFamily: 'Arial'
        }).setOrigin(0.5);

        // 方块预览区域背景
        const blockPreviewBg = this.add.graphics();
        blockPreviewBg.fillStyle(0x34495e, 0.8);
        blockPreviewBg.fillRoundedRect(20, blockPreviewY - 80, 710, 200, 10);
        blockPreviewBg.lineStyle(2, 0x2c3e50);
        blockPreviewBg.strokeRoundedRect(20, blockPreviewY - 80, 710, 200, 10);


    }

    // 创建波次怪物
    function createWave() {
        isCreatingWave = true;

        // 清除现有怪物和血条
        monsters.forEach(monster => monster.destroy());
        monsters = [];

        if (this.monsterHealthBars) {
            this.monsterHealthBars.forEach(bar => {
                if (bar) bar.destroy();
            });
            this.monsterHealthBars = [];
        }

        // 创建新怪物 - 排成一行，底部与角色对齐
        const monsterSpacing = 80;
        const startX = 450;
        const startY = player.y;

        for (let i = 0; i < totalMonstersInWave; i++) {
            const xPos = startX + i * monsterSpacing;
            const yPos = startY;

            // 随机选择怪物图片 (1-15)
            const monsterImageIndex = Math.floor(Math.random() * 15) + 1;

            let monster = this.add.image(xPos, yPos, `monster_${monsterImageIndex}`);
            monster.setScale(0.5);
            monster.setOrigin(0.5, 1);
            monster.setDepth(100 + yPos * 0.1);

            monster.health = 30 + currentLevel * 10;
            monster.maxHealth = monster.health;
            monster.lastAttack = 0;
            monster.isMoving = false;
            monster.originalX = xPos;
            monster.originalY = yPos;
            monster.jumpTween = null;
            monsters.push(monster);
        }

        monstersKilled = 0;

        setTimeout(() => {
            updateMonsterHealthBars.call(this);
            isCreatingWave = false;
        }, 50);
    }


    // 主角远程攻击系统
    function playerAttack() {
        if (monsters.length > 0) {
            // 简化为单目标攻击
            const target = monsters[0];

            // 使用主武器位置
            const weaponX = playerWeapon.x + 40;
            const weaponY = playerWeapon.y - 50;

            const projectile = this.add.circle(weaponX, weaponY, 4, 0xffff00);

            // 攻击动画
            this.tweens.add({
                targets: projectile,
                x: target.x - 30,
                y: target.y - 100, // 击中怪物中间位置，而不是底部
                duration: 200, // 子弹飞行速度
                    onComplete: () => {
                        projectile.destroy();

                        // 计算基于方块数量的伤害
                        let damage = calculateAttackDamage() + currentLevel * 2;

                        // 暴击判定
                        const isCrit = Math.random() < playerStats.critChance;
                        if (isCrit) {
                            damage *= 2;
                            // 暴击特效
                            const critText = this.add.text(target.x, target.y - 80, 'CRIT!', {
                                fontSize: '20px',
                                fill: '#e74c3c',
                                fontFamily: 'Arial',
                                fontWeight: 'bold'
                            }).setOrigin(0.5);

                            this.tweens.add({
                                targets: critText,
                                alpha: 0,
                                y: critText.y - 30,
                                duration: 1000,
                                onComplete: () => critText.destroy()
                            });
                        }

                        // 造成伤害
                        target.health -= damage;

                        // 显示伤害飘字
                        showDamageText.call(this, target, damage);

                        // 怪物受击效果：闪红并停顿
                        monsterHitEffect.call(this, target);

                        // 更新血条
                        updateMonsterHealthBars.call(this);

                        // 生命偷取
                        if (playerStats.lifeSteal > 0) {
                            const healAmount = Math.floor(damage * playerStats.lifeSteal);
                            playerHealth = Math.min(maxPlayerHealth, playerHealth + healAmount);

                            // 治疗特效
                            if (healAmount > 0) {
                                const healText = this.add.text(player.x, player.y - 60, `+${healAmount}`, {
                                    fontSize: '16px',
                                    fill: '#2ecc71',
                                    fontFamily: 'Arial'
                                }).setOrigin(0.5);

                                this.tweens.add({
                                    targets: healText,
                                    alpha: 0,
                                    y: healText.y - 20,
                                    duration: 1000,
                                    onComplete: () => healText.destroy()
                                });
                            }
                        }

                        // 伤害特效 - 减小闪动幅度
                        this.tweens.add({
                            targets: target,
                            scaleX: 0.27, // 轻微放大，避免过大闪动
                            scaleY: 0.27,
                            duration: 150,
                            yoyo: true
                        });

                        // 检查怪物是否死亡
                        if (target.health <= 0) {
                            target.destroy();
                            const index = monsters.indexOf(target);
                            if (index > -1) {
                                monsters.splice(index, 1);
                                monstersKilled++;
                            }
                        }
                    }
                });

            // 主角攻击动画 - 更轻微的抖动
            this.tweens.add({
                targets: player,
                x: player.x + 2, // 减少到2像素，更轻微
                duration: 60,
                yoyo: true,
                ease: 'Power2'
            });

            // 武器开火抖动
            if (playerWeapon) {
                animateWeaponRecoil.call(this, playerWeapon);
            }
        }
    }

    // 武器后坐力动画
    function animateWeaponRecoil(weapon) {
        const originalWeaponX = weapon.x;
        const originalWeaponY = weapon.y;

        // 水平后坐力
        this.tweens.add({
            targets: weapon,
            x: originalWeaponX - 8, // 向后抖动8像素
            duration: 80,
            ease: 'Power2',
            onComplete: () => {
                // 回到原位
                this.tweens.add({
                    targets: weapon,
                    x: originalWeaponX,
                    duration: 40,
                    ease: 'Power2'
                });
            }
        });

        // 垂直抖动
        this.tweens.add({
            targets: weapon,
            y: originalWeaponY - 4, // 向上抖动4像素
            duration: 80,
            ease: 'Power2',
            onComplete: () => {
                // 回到原位
                this.tweens.add({
                    targets: weapon,
                    y: originalWeaponY,
                    duration: 40,
                    ease: 'Power2'
                });
            }
        });
    }

    // 游戏更新循环
    function update(time, delta) {
        // 更新UI（无论什么状态都要更新）
        updateUI.call(this);

        // 更新血条
        updatePlayerHealthBar.call(this);
        updateEnemyHealthBar.call(this);



        // 更新飞行方块（无论什么状态都要更新）
        updateFlyingBlocks.call(this, time, delta);

        if (gameState === 'fighting') {
            // 更新战斗逻辑
            updateBattle.call(this, time, delta);

            // 检查波次完成
            checkWaveComplete.call(this);

            // 检查游戏结束
            if (playerHealth <= 0) {
                gameState = 'gameOver';
                this.add.text(375, 667, 'GAME OVER', {
                    fontSize: '48px',
                    fill: '#e74c3c',
                    fontFamily: 'Arial'
                }).setOrigin(0.5);
            }
        }

        // 更新关卡和波次显示
        if (this.levelText) {
            this.levelText.setText(`关卡 ${currentLevel}`);
        }
        if (this.waveText) {
            this.waveText.setText(`波次 ${currentWave}`);
        }
    }

    // 更新战斗逻辑
    function updateBattle(time, delta) {
        // 主角自动攻击 - 使用动态攻击速度
        if (!this.lastPlayerAttack) this.lastPlayerAttack = 0;
        if (time - this.lastPlayerAttack > playerStats.attackSpeed && monsters.length > 0) {
            playerAttack.call(this);
            this.lastPlayerAttack = time;
        }

        // 怪物AI - 移动到主角身体边并攻击
        monsters.forEach((monster, index) => {

            const distanceToPlayer = Phaser.Math.Distance.Between(monster.x, monster.y, player.x, player.y);

            // 如果距离主角太远，移动靠近
            if (distanceToPlayer > 80 && !monster.isMoving) {
                monster.isMoving = true;

                // 计算移动目标位置，避免重叠
                let targetX, targetY;
                let attempts = 0;
                let validPosition = false;

                while (!validPosition && attempts < 10) {
                    // 根据怪物索引分配不同的角度，避免重叠
                    const angle = (index * (Math.PI * 2 / totalMonstersInWave)) + Math.random() * 0.5;
                    const distance =  30+Math.random() * 30;

                    targetX = player.x+80 + Math.cos(angle) * distance;
                    targetY = player.y + Math.sin(angle) * 10; // Y轴范围限制在±20

                    // 检查是否与其他怪物位置冲突
                    validPosition = true;
                    for (let otherMonster of monsters) {
                        if (otherMonster !== monster) {
                            const distanceToOther = Phaser.Math.Distance.Between(targetX, targetY, otherMonster.x, otherMonster.y);
                            if (distanceToOther < 50) { // 最小间距50像素
                                validPosition = false;
                                break;
                            }
                        }
                    }
                    attempts++;
                }

                // 如果找不到合适位置，使用默认位置
                if (!validPosition) {
                    targetX = player.x + 60 + index * 30;
                    targetY = player.y + (index % 2 === 0 ? -15 : 15);
                }

                // 蹦蹦跳跳的移动动画 - 进一步减慢速度
                this.tweens.add({
                    targets: monster,
                    x: targetX,
                    y: targetY,
                    duration: 25000, // 从10000增加到15000，移动更慢
                    ease: 'Power2',
                    onComplete: () => {
                        monster.isMoving = false;
                        // 停止跳跃动画
                        if (monster.jumpTween) {
                            monster.jumpTween.stop();
                            monster.jumpTween = null;
                        }
                        // 移动完成后更新血条位置
                        updateHealthBarPositions.call(this);
                    }
                });

                // 添加跳跃效果 - 上下弹跳
                monster.jumpTween = this.tweens.add({
                    targets: monster,
                    scaleY: 0.52, // 压扁效果（适配新的基础尺寸0.25）
                    duration: 200,
                    yoyo: true,
                    repeat: -1, // 无限重复
                    ease: 'Sine.easeInOut'
                });
            }

            // 如果靠近主角，进行攻击
            if (distanceToPlayer <= 100 && time - monster.lastAttack > 2500) { // 每2.5秒攻击一次
                let damage = 12 + currentLevel;
                playerHealth -= damage;

                monster.lastAttack = time;

                // 停止跳跃动画（如果正在跳跃）
                if (monster.jumpTween) {
                    monster.jumpTween.stop();
                    monster.jumpTween = null;
                }

                // 攻击倾斜动画 - 向主角方向倾斜
                this.tweens.add({
                    targets: monster,
                    rotation: -0.4, // 向左倾斜约23度
                    duration: 150,
                    yoyo: true,
                    ease: 'Power2'
                });

                // 近战攻击动画 - 向主角方向冲刺
                const currentX = monster.x;
                const currentY = monster.y;
                this.tweens.add({
                    targets: monster,
                    x: currentX - 30,
                    y: currentY,
                    duration: 200,
                    yoyo: true,
                    ease: 'Power2',
                    onComplete: () => {
                        // 攻击动画完成后更新血条位置
                        updateHealthBarPositions.call(this);
                    }
                });

                // 攻击时的缩放效果
                this.tweens.add({
                    targets: monster,
                    scaleX: 0.28, // 攻击时稍微放大（适配新的基础尺寸0.25）
                    scaleY: 0.28,
                    duration: 150,
                    yoyo: true
                });

                // 主角受击效果
                this.tweens.add({
                    targets: player,
                    tint: 0xff0000,
                    duration: 100,
                    yoyo: true,
                    onComplete: () => {
                        player.clearTint();
                    }
                });
            }
        });
    }

    // 更新UI
    function updateUI() {
        // 更新血条位置以跟随怪物移动
        updateHealthBarPositions.call(this);
        // 更新怪物深度层级
        updateMonsterDepths.call(this);
    }

    // 更新怪物和主角深度层级，根据Y轴位置
    function updateMonsterDepths() {
        // 更新主角深度
        if (player) {
            player.setDepth(100 + player.y * 0.1);
        }

        // 更新武器深度
        if (playerWeapon) {
            playerWeapon.setDepth(100 + player.y * 0.1 + 1);
        }

        // 更新怪物深度
        monsters.forEach(monster => {
            // Y轴越大（越靠下）深度越大，显示在前面
            // 基础深度100，加上Y轴位置的0.1倍作为偏移
            monster.setDepth(100 + monster.y * 0.1);
        });
    }

    // 更新血条位置以跟随怪物移动
    function updateHealthBarPositions() {
        if (!this.monsterHealthBars) return;

        // 更新武器位置跟随主角
        if (playerWeapon && player) {
            playerWeapon.x = player.x + 20;
            playerWeapon.y = player.y - 30;
        }

        monsters.forEach((monster, index) => {
            if (monster.health > 0) {
                const baseIndex = index * 2; // 每个怪物2个元素：背景、前景

                // 更新血条背景位置和深度
                if (this.monsterHealthBars[baseIndex]) {
                    this.monsterHealthBars[baseIndex].x = monster.x;
                    this.monsterHealthBars[baseIndex].y = monster.y - 140;
                    this.monsterHealthBars[baseIndex].setDepth(100 + monster.y * 0.1 + 50);
                }

                // 更新血条前景位置和深度
                if (this.monsterHealthBars[baseIndex + 1]) {
                    const healthPercent = monster.health / monster.maxHealth;
                    const currentWidth = 48 * healthPercent;
                    this.monsterHealthBars[baseIndex + 1].x = monster.x - 25; // 血条左边缘位置
                    this.monsterHealthBars[baseIndex + 1].y = monster.y - 140;
                    this.monsterHealthBars[baseIndex + 1].width = currentWidth;
                    this.monsterHealthBars[baseIndex + 1].setDepth(100 + monster.y * 0.1 + 51);
                }

                // 移除血量数字位置更新
            }
        });
    }

    // 更新怪物头顶血条 - 优化版本，减少闪动
    function updateMonsterHealthBars() {
        // 初始化血条数组
        if (!this.monsterHealthBars) {
            this.monsterHealthBars = [];
        }

        // 清除多余的血条（当怪物数量减少时）
        while (this.monsterHealthBars.length > monsters.length * 2) {
            const bar = this.monsterHealthBars.pop();
            if (bar) bar.destroy();
        }

        // 为每个怪物更新或创建血条
        monsters.forEach((monster, index) => {
            if (monster.health > 0) {
                const healthPercent = monster.health / monster.maxHealth;
                const baseIndex = index * 2; // 每个怪物2个元素：背景、前景

                // 血条背景
                if (!this.monsterHealthBars[baseIndex]) {
                    const barBg = this.add.rectangle(
                        monster.x,
                        monster.y - 140,
                        50,
                        8,
                        0x2c3e50
                    );
                    barBg.setStrokeStyle(1, 0x000000);
                    // 血条深度比对应怪物高一些，确保显示在怪物上方
                    barBg.setDepth(100 + monster.y * 0.1 + 50);
                    this.monsterHealthBars[baseIndex] = barBg;
                } else {
                    // 更新位置
                    this.monsterHealthBars[baseIndex].x = monster.x;
                    this.monsterHealthBars[baseIndex].y = monster.y - 140;
                }

                // 血条前景
                const currentWidth = 48 * healthPercent;
                if (currentWidth > 0) {
                    // 血条颜色默认为红色
                    let barColor = 0xe74c3c; // 红色

                    if (!this.monsterHealthBars[baseIndex + 1]) {
                        const bar = this.add.rectangle(
                            monster.x - 25, // 血条左边缘位置
                            monster.y - 140,
                            currentWidth,
                            6,
                            barColor
                        );
                        bar.setOrigin(0, 0.5); // 设置原点在左边中间，这样血条从左边开始填充
                        bar.setDepth(100 + monster.y * 0.1 + 51);
                        this.monsterHealthBars[baseIndex + 1] = bar;
                    } else {
                        // 更新血条
                        const bar = this.monsterHealthBars[baseIndex + 1];
                        bar.x = monster.x - 25; // 血条左边缘位置
                        bar.y = monster.y - 140;
                        bar.width = currentWidth;
                        bar.fillColor = barColor;
                        bar.setVisible(true);
                    }
                } else if (this.monsterHealthBars[baseIndex + 1]) {
                    // 血量为0时隐藏血条
                    this.monsterHealthBars[baseIndex + 1].setVisible(false);
                }

                // 移除血量数字显示
            }
        });
    }

    // 检查波次完成
    function checkWaveComplete() {
        // 如果怪物全部死亡且没有正在创建新波次
        if (monsters.length === 0 && !isCreatingWave) {
            isCreatingWave = true;

            currentWave++;

            if (currentWave > 3) {
                currentLevel++;
                currentWave = 1;
                totalMonstersInWave = Math.min(6, 3 + Math.floor(currentLevel / 2));
            }

            // 回到准备阶段，显示战斗按钮
            gameState = 'preparing';
            if (battleButton) {
                battleButton.setVisible(true);
                battleButton.setText('下一波战斗');
            }

            isCreatingWave = false;
        }
    }

    // 创建UI元素
    function createUI() {
        // 左上角玩家头像
        const playerAvatarBg = this.add.graphics();
        playerAvatarBg.fillStyle(0xffffff); // 蓝色背景
        playerAvatarBg.lineStyle(3, 0xffffff); // 黑色边框
        playerAvatarBg.fillRoundedRect(15, 15, 80, 80, 40); // 圆角矩形
        playerAvatarBg.strokeRoundedRect(15, 15, 80, 80, 40);

        const playerAvatar = this.add.text(55, 55, '🤴', {
            fontSize: '80px'
        }).setOrigin(0.5);

        const playerLabel = this.add.text(105, 20, '玩家10986', {
            fontSize: '20px',
            fill: '#000000',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        });

        // 玩家血条背景
        const playerHealthBarBg = this.add.graphics();
        playerHealthBarBg.fillStyle(0x2c3e50); // 深色背景
        playerHealthBarBg.lineStyle(2, 0x000000); // 黑色边框
        playerHealthBarBg.fillRoundedRect(100, 45, 150, 15, 7); // 圆角矩形
        playerHealthBarBg.strokeRoundedRect(100, 45, 150, 15, 7);

        // 玩家血条
        this.playerHealthBar = this.add.graphics();
        updatePlayerHealthBar.call(this);

        // 玩家血条文字
        this.playerHealthText = this.add.text(175, 52, '', {
            fontSize: '15px',
            fill: '#ffffff',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5);

        // 右上角敌方头像和血条
        const enemyAvatarBg = this.add.graphics();
        enemyAvatarBg.fillStyle(0xe74c3c); // 红色背景
        enemyAvatarBg.lineStyle(3, 0x000000); // 黑色边框
        enemyAvatarBg.fillRoundedRect(655, 15, 80, 80, 40); // 圆角矩形
        enemyAvatarBg.strokeRoundedRect(655, 15, 80, 80, 40);

        const enemyAvatar = this.add.text(695, 60, '🧟', {
            fontSize: '80px'
        }).setOrigin(0.5);

        const enemyLabel = this.add.text(605, 30, '敌方', {
            fontSize: '20px',
            fill: '#000000',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5);

        // 敌方血条背景 - 移到头像右侧同一行
        const healthBarBg = this.add.graphics();
        healthBarBg.fillStyle(0x2c3e50); // 深色背景
        healthBarBg.lineStyle(2, 0x000000); // 黑色边框
        healthBarBg.fillRoundedRect(480, 45, 150, 15, 7); // 圆角矩形，与头像同一行
        healthBarBg.strokeRoundedRect(480, 45, 150, 15, 7);

        // 敌方血条
        this.enemyHealthBar = this.add.graphics();
        updateEnemyHealthBar.call(this);

        // 血条文字
        this.enemyHealthText = this.add.text(555, 52, '', {
            fontSize: '15px',
            fill: '#ffffff',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5);

        // 上方中间关卡和波次信息
        const levelBg = this.add.graphics();
        levelBg.fillStyle(0x34495e, 0.8); // 半透明深色背景
        levelBg.lineStyle(2, 0x000000); // 黑色边框
        levelBg.fillRoundedRect(300, 15, 150, 60, 10); // 圆角矩形
        levelBg.strokeRoundedRect(300, 15, 150, 60, 10);

        this.levelText = this.add.text(375, 35, `关卡 ${currentLevel}`, {
            fontSize: '26px',
            fill: '#ffffff',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5);

        this.waveText = this.add.text(375, 58, `波次 ${currentWave}`, {
            fontSize: '14px',
            fill: '#ecf0f1',
            fontFamily: 'Arial'
        }).setOrigin(0.5);




    }

    // 更新玩家血条
    function updatePlayerHealthBar() {
        if (!this.playerHealthBar) return;

        this.playerHealthBar.clear();

        const healthPercent = Math.max(0, playerHealth) / maxPlayerHealth;
        const barWidth = 146; // 血条宽度（减去边框）
        const currentWidth = barWidth * healthPercent;

        // 血条颜色根据血量变化
        let barColor = 0x27ae60; // 绿色
        if (healthPercent < 0.6) barColor = 0xf39c12; // 橙色
        if (healthPercent < 0.3) barColor = 0xe74c3c; // 红色

        this.playerHealthBar.fillStyle(barColor);
        this.playerHealthBar.fillRoundedRect(102, 47, currentWidth, 11, 5); // 圆角血条

        // 更新血条文字
        if (this.playerHealthText) {
            this.playerHealthText.setText(`${Math.max(0, playerHealth)}/${maxPlayerHealth}`);
        }
    }

    // 更新敌方进度条（波次怪物进度）
    function updateEnemyHealthBar() {
        if (!this.enemyHealthBar) return;

        this.enemyHealthBar.clear();

        // 计算波次进度：已击杀的怪物 / 总怪物数
        const totalMonstersInWave = 3 + currentLevel; // 每波怪物总数
        const remainingMonsters = monsters.length; // 剩余怪物数
        const killedMonsters = totalMonstersInWave - remainingMonsters; // 已击杀怪物数

        const progressPercent = killedMonsters / totalMonstersInWave;
        const barWidth = 146; // 进度条宽度（减去边框）
        const currentWidth = barWidth * progressPercent;

        // 进度条颜色根据进度变化
        let barColor = 0xe74c3c; // 红色（开始）
        if (progressPercent > 0.3) barColor = 0xf39c12; // 橙色
        if (progressPercent > 0.6) barColor = 0x27ae60; // 绿色（接近完成）

        this.enemyHealthBar.fillStyle(barColor);
        this.enemyHealthBar.fillRoundedRect(482, 47, currentWidth, 11, 5); // 圆角进度条

        // 更新进度条文字
        if (this.enemyHealthText) {
            this.enemyHealthText.setText(`${killedMonsters}/${totalMonstersInWave}`);
        }
    }
    // 重新排列怪物位置
    function repositionMonsters() {
        const monsterSpacing = 80;
        const startX = 450;
        const startY = player.y; // 与角色底部对齐

        monsters.forEach((monster, index) => {
            const newX = startX + index * monsterSpacing;

            // 平滑移动到新位置
            this.tweens.add({
                targets: monster,
                x: newX,
                y: startY, // 确保Y位置也对齐
                duration: 500,
                ease: 'Power2',
                onComplete: () => {
                    // 移动完成后更新血条位置
                    updateHealthBarPositions.call(this);
                }
            });
        });
    }





    // 怪物受击效果：闪红并停顿
    function monsterHitEffect(monster) {
        // 停止怪物的移动动画
        if (monster.jumpTween) {
            monster.jumpTween.pause();
        }

        // 暂停怪物移动状态
        const wasMoving = monster.isMoving;
        monster.isMoving = false;

        // 闪红效果
        monster.setTint(0xff0000); // 设置红色

        // 轻微震动效果
        const originalX = monster.x;
        const originalY = monster.y;

        this.tweens.add({
            targets: monster,
            x: originalX + 3,
            duration: 50,
            yoyo: true,
            repeat: 2, // 震动3次
            ease: 'Power2',
            onComplete: () => {
                // 恢复原色
                monster.clearTint();

                // 恢复移动状态
                monster.isMoving = wasMoving;
                if (monster.jumpTween && wasMoving) {
                    monster.jumpTween.resume();
                }
            }
        });
    }

    // 显示怪物伤害飘字
    function showDamageText(monster, damage) {
        // 创建伤害文字
        const damageText = this.add.text(
            monster.x + (Math.random() - 0.5) * 20, // 随机偏移位置
            monster.y - 60,
            `-${Math.floor(damage)}`,
            {
                fontSize: '18px',
                fill: '#ff4444',
                fontFamily: 'Arial',
                fontWeight: 'bold',
                stroke: '#000000',
                strokeThickness: 2
            }
        ).setOrigin(0.5);

        // 设置深度确保显示在最上层
        damageText.setDepth(200);

        // 飘字动画
        this.tweens.add({
            targets: damageText,
            y: damageText.y - 40, // 向上飘
            alpha: 0, // 逐渐透明
            scale: 1.2, // 稍微放大
            duration: 800,
            ease: 'Power2',
            onComplete: () => {
                damageText.destroy();
            }
        });
    }
    // ========== 方块游戏系统 ==========

    // 创建方块纹理
    function createBlockTextures() {
        const graphics = this.add.graphics();

        // 创建三种颜色的方块纹理
        blockColors.forEach((colorInfo, index) => {
            graphics.clear();
            graphics.fillStyle(colorInfo.color);
            graphics.fillRoundedRect(0, 0, cellSize-2, cellSize-2, 8);
            graphics.lineStyle(3, colorInfo.color - 0x222222); // 稍微深一点的边框
            graphics.strokeRoundedRect(0, 0, cellSize-2, cellSize-2, 8);
            graphics.generateTexture(`blockTexture_${colorInfo.name}`, cellSize, cellSize);
        });

        // 创建网格背景纹理
        graphics.clear();
        graphics.fillStyle(0x34495e);
        graphics.fillRoundedRect(0, 0, cellSize-1, cellSize-1, 5);
        graphics.lineStyle(2, 0x2c3e50);
        graphics.strokeRoundedRect(0, 0, cellSize-1, cellSize-1, 5);
        graphics.generateTexture('gridCellTexture', cellSize, cellSize);

        // 创建高亮方块纹理
        graphics.clear();
        graphics.fillStyle(0xffdc35);
        graphics.fillRoundedRect(0, 0, cellSize-2, cellSize-2, 8);
        graphics.lineStyle(3, 0xe6c62f);
        graphics.strokeRoundedRect(0, 0, cellSize-2, cellSize-2, 8);
        graphics.generateTexture('highlightBlockTexture', cellSize, cellSize);

        // 创建一个通用的方块纹理作为备用
        graphics.clear();
        graphics.fillStyle(0x3498db);
        graphics.fillRoundedRect(0, 0, cellSize-2, cellSize-2, 8);
        graphics.lineStyle(3, 0x2980b9);
        graphics.strokeRoundedRect(0, 0, cellSize-2, cellSize-2, 8);
        graphics.generateTexture('blockTexture', cellSize, cellSize);

        graphics.destroy();
    }

    // 初始化方块游戏网格
    function createBlockGrid() {
        // 初始化游戏网格数据
        grid = [];
        blockGridSprites = [];
        for (let row = 0; row < gridSize; row++) {
            grid[row] = [];
            blockGridSprites[row] = [];
            for (let col = 0; col < gridSize; col++) {
                grid[row][col] = {
                    filled: false,
                    sprite: null,
                    previewSprite: null,
                    color: null
                };
                blockGridSprites[row][col] = null;
            }
        }

        // 创建网格视觉效果
        for (let row = 0; row < gridSize; row++) {
            for (let col = 0; col < gridSize; col++) {
                const x = gridOffsetX + col * cellSize;
                const y = gridOffsetY + row * cellSize;
                const cell = this.add.image(x, y, 'gridCellTexture');
                cell.setOrigin(0, 0);
            }
        }
    }

    // 生成新方块
    function generateNewBlocks() {
        // 清除当前方块
        currentBlocks.forEach(blockGroup => {
            if (blockGroup.container) {
                blockGroup.container.destroy();
            }
        });

        currentBlocks = [];

        // 生成3个新方块
        for (let i = 0; i < 3; i++) {
            const shapeIndex = Phaser.Math.Between(0, blockShapes.length - 1);
            const shape = blockShapes[shapeIndex];
            const blockGroup = createBlockGroup.call(this, shape, i);
            currentBlocks.push(blockGroup);
        }
    }

    // 创建方块组
    function createBlockGroup(shape, index) {
        const startX = 100 + index * 180; // 调整起始位置适应更大的方块
        const startY = blockPreviewY;

        const container = this.add.container(startX, startY);
        const blocks = [];

        // 为这个方块组随机选择一种颜色
        const colorInfo = blockColors[Math.floor(Math.random() * blockColors.length)];

        // 创建方块组
        for (let row = 0; row < shape.length; row++) {
            for (let col = 0; col < shape[row].length; col++) {
                if (shape[row][col] === 1) {
                    // 检查纹理是否存在
                    let textureName = `blockTexture_${colorInfo.name}`;
                    if (!this.textures.exists(textureName)) {
                        textureName = 'blockTexture'; // 使用备用纹理
                    }

                    const block = this.add.image(
                        col * cellSize,
                        row * cellSize,
                        textureName
                    );
                    block.setOrigin(0, 0);

                    // 如果使用备用纹理，设置对应的颜色
                    if (textureName === 'blockTexture') {
                        block.setTint(colorInfo.color);
                    }

                    // 给每个方块添加交互事件
                    block.setInteractive();
                    block.blockGroupIndex = index;
                    block.shapeRow = row;
                    block.shapeCol = col;
                    block.blockColor = colorInfo; // 存储方块颜色信息

                    container.add(block);
                    blocks.push(block);
                }
            }
        }

        // 设置容器大小
        container.setSize(shape[0].length * cellSize, shape.length * cellSize);

        return {
            container: container,
            shape: shape,
            blocks: blocks,
            originalX: startX,
            originalY: startY,
            used: false,
            isDragging: false,
            colorInfo: colorInfo // 存储整个方块组的颜色信息
        };
    }

    // 设置方块游戏输入
   // 设置方块游戏输入
    function setupBlockInput() {
        let dragStartPos = { x: 0, y: 0 };
        let dragOffset = { x: 0, y: 0 };

        // 鼠标/触摸按下
        this.input.on('pointerdown', function (pointer, currentlyOver) {
            if (currentlyOver.length > 0) {
                const gameObject = currentlyOver[0];

                // 检查是否点击了方块
                if (gameObject.blockGroupIndex !== undefined) {
                    const blockGroup = currentBlocks[gameObject.blockGroupIndex];

                    if (blockGroup && !blockGroup.used && gameState === 'preparing') {
                        // 检查是否在方块预览区域内
                        if (pointer.y >= blockPreviewY - 150 && pointer.y <= blockPreviewY + 200) {
                            draggedBlock = blockGroup;
                            draggedBlock.isDragging = true;

                            // 记录拖拽开始位置和偏移
                            dragStartPos.x = pointer.x;
                            dragStartPos.y = pointer.y;

                            // 计算点击位置相对于容器的偏移
                            dragOffset.x = pointer.x - draggedBlock.container.x;
                            dragOffset.y = pointer.y - draggedBlock.container.y;

                            // 视觉效果
                            draggedBlock.container.setScale(1.1);
                            draggedBlock.container.setDepth(100);
                            draggedBlock.container.setAlpha(0.8);
                        }
                    }
                }
            }
        }, this); // <--- 在这里添加 this

        // 鼠标/触摸移动
        this.input.on('pointermove', function (pointer) {
            if (draggedBlock && draggedBlock.isDragging && !draggedBlock.used) {
                // 更新容器位置
                let newX = pointer.x - dragOffset.x;
                let newY = pointer.y - dragOffset.y;

                draggedBlock.container.x = newX;
                draggedBlock.container.y = newY;

                // 检查是否可以放置并显示预览
                checkBlockPlacementPreview.call(this, newX, newY);
            }
        }, this); // <--- 在这里添加 this

        // 鼠标/触摸释放
        this.input.on('pointerup', function (pointer) {
            if (draggedBlock && draggedBlock.isDragging && !draggedBlock.used) {
                const canPlace = tryPlaceBlockOnGrid.call(this, draggedBlock.container.x, draggedBlock.container.y);

                if (canPlace) {
                    placeBlockOnGrid.call(this);

                    // 保存对当前拖拽方块的引用
                    const currentDraggedBlock = draggedBlock;
                    currentDraggedBlock.used = true;

                    // 放置成功的视觉反馈
                    this.tweens.add({
                        targets: currentDraggedBlock.container,
                        alpha: 0,
                        scaleX: 0.8,
                        scaleY: 0.8,
                        duration: 200,
                        ease: 'Power2.easeIn',
                        onComplete: () => {
                            if (currentDraggedBlock.container) {
                                currentDraggedBlock.container.setVisible(false);
                            }
                        }
                    });

                    // 检查消除
                    setTimeout(() => {
                        checkAndClearBlockLines.call(this);

                        // 检查是否所有方块都已使用
                        if (currentBlocks.every(b => b.used)) {
                            setTimeout(() => {
                                generateNewBlocks.call(this);
                            }, 300);
                        }
                    }, 200);
                } else {
                    // 返回原位置
                    const currentDraggedBlock = draggedBlock;
                    this.tweens.add({
                        targets: currentDraggedBlock.container,
                        x: currentDraggedBlock.originalX,
                        y: currentDraggedBlock.originalY,
                        duration: 300,
                        ease: 'Back.easeOut'
                    });
                }

                // 恢复视觉效果
                if (draggedBlock && draggedBlock.container) {
                    draggedBlock.container.setScale(1);
                    draggedBlock.container.setDepth(0);
                    draggedBlock.container.setAlpha(1);
                    draggedBlock.isDragging = false;
                }
                clearBlockPlacementPreview.call(this);
            }

            draggedBlock = null;
        }, this); // <--- 在这里添加 this
    }
    // 检查方块放置预览
    function checkBlockPlacementPreview(x, y) {
        clearBlockPlacementPreview.call(this);

        if (!draggedBlock) return;

        // 使用容器的左上角位置来计算网格位置
        const gridPos = screenToBlockGrid(x, y);

        if (canPlaceBlockAt(draggedBlock.shape, gridPos.row, gridPos.col)) {
            showBlockPlacementPreview.call(this, draggedBlock.shape, gridPos.row, gridPos.col);
            return true;
        }
        return false;
    }

    // 清除方块放置预览
    function clearBlockPlacementPreview() {
        for (let row = 0; row < gridSize; row++) {
            for (let col = 0; col < gridSize; col++) {
                if (grid[row][col].previewSprite) {
                    grid[row][col].previewSprite.destroy();
                    grid[row][col].previewSprite = null;
                }
            }
        }
    }

    // 显示方块放置预览
    function showBlockPlacementPreview(shape, startRow, startCol) {
        for (let row = 0; row < shape.length; row++) {
            for (let col = 0; col < shape[row].length; col++) {
                if (shape[row][col] === 1) {
                    const gridRow = startRow + row;
                    const gridCol = startCol + col;

                    if (gridRow >= 0 && gridRow < gridSize && gridCol >= 0 && gridCol < gridSize) {
                        const x = gridOffsetX + gridCol * cellSize;
                        const y = gridOffsetY + gridRow * cellSize;

                        // 检查纹理是否存在，如果不存在则使用备用纹理
                        let textureName = 'highlightBlockTexture';
                        if (!this.textures.exists(textureName)) {
                            textureName = 'blockTexture'; // 使用备用纹理
                        }

                        const preview = this.add.image(x, y, textureName);
                        preview.setOrigin(0, 0);
                        preview.setAlpha(0.6);
                        preview.setTint(0xffdc35); // 设置高亮颜色
                        grid[gridRow][gridCol].previewSprite = preview;
                    }
                }
            }
        }
    }

    // 屏幕坐标转换为网格坐标
    function screenToBlockGrid(screenX, screenY) {
        const col = Math.round((screenX - gridOffsetX) / cellSize);
        const row = Math.round((screenY - gridOffsetY) / cellSize);
        return { row, col };
    }

    // 网格坐标转换为屏幕坐标
    function blockGridToScreen(row, col) {
        return {
            x: gridOffsetX + col * cellSize,
            y: gridOffsetY + row * cellSize
        };
    }

    // 检查是否可以在指定位置放置方块
    function canPlaceBlockAt(shape, startRow, startCol) {
        for (let row = 0; row < shape.length; row++) {
            for (let col = 0; col < shape[row].length; col++) {
                if (shape[row][col] === 1) {
                    const gridRow = startRow + row;
                    const gridCol = startCol + col;

                    // 检查边界
                    if (gridRow < 0 || gridRow >= gridSize || gridCol < 0 || gridCol >= gridSize) {
                        return false;
                    }

                    // 检查是否已被占用
                    if (grid[gridRow][gridCol].filled) {
                        return false;
                    }
                }
            }
        }
        return true;
    }

    // 尝试放置方块
    function tryPlaceBlockOnGrid(screenX, screenY) {
        if (!draggedBlock || !draggedBlock.container) return false;

        const gridPos = screenToBlockGrid(screenX, screenY);

        // 检查是否可以在计算出的网格位置放置方块
        if (canPlaceBlockAt(draggedBlock.shape, gridPos.row, gridPos.col)) {
            // 如果可以放置，将容器位置对齐到网格
            const alignedPos = blockGridToScreen(gridPos.row, gridPos.col);
            draggedBlock.container.x = alignedPos.x;
            draggedBlock.container.y = alignedPos.y;
            return true;
        }

        return false;
    }

    // 在网格上放置方块
    function placeBlockOnGrid() {
        if (!draggedBlock || !draggedBlock.container) return;

        const gridPos = screenToBlockGrid(draggedBlock.container.x, draggedBlock.container.y);
        const shape = draggedBlock.shape;

        // 在网格中放置方块
        for (let row = 0; row < shape.length; row++) {
            for (let col = 0; col < shape[row].length; col++) {
                if (shape[row][col] === 1) {
                    const gridRow = gridPos.row + row;
                    const gridCol = gridPos.col + col;

                    if (gridRow >= 0 && gridRow < gridSize && gridCol >= 0 && gridCol < gridSize) {
                        grid[gridRow][gridCol].filled = true;
                        grid[gridRow][gridCol].color = draggedBlock.colorInfo; // 记录方块颜色

                        // 创建方块精灵
                        const x = gridOffsetX + gridCol * cellSize;
                        const y = gridOffsetY + gridRow * cellSize;

                        // 检查纹理是否存在
                        let textureName = `blockTexture_${draggedBlock.colorInfo.name}`;
                        if (!this.textures.exists(textureName)) {
                            textureName = 'blockTexture'; // 使用备用纹理
                        }

                        const blockSprite = this.add.image(x, y, textureName);
                        blockSprite.setOrigin(0, 0);

                        // 如果使用备用纹理，设置对应的颜色
                        if (textureName === 'blockTexture') {
                            blockSprite.setTint(draggedBlock.colorInfo.color);
                        }

                        // 添加放置动画
                        blockSprite.setScale(0);
                        this.tweens.add({
                            targets: blockSprite,
                            scaleX: 1,
                            scaleY: 1,
                            duration: 200,
                            ease: 'Back.easeOut',
                            delay: (row + col) * 50
                        });

                        grid[gridRow][gridCol].sprite = blockSprite;
                        blockGridSprites[gridRow][gridCol] = blockSprite;
                    }
                }
            }
        }
    }

    // 检查并清除满行/满列
    function checkAndClearBlockLines() {
        const linesToClear = [];

        // 检查行
        for (let row = 0; row < gridSize; row++) {
            let fullRow = true;
            for (let col = 0; col < gridSize; col++) {
                if (!grid[row][col].filled) {
                    fullRow = false;
                    break;
                }
            }
            if (fullRow) {
                linesToClear.push({ type: 'row', index: row });
            }
        }

        // 检查列
        for (let col = 0; col < gridSize; col++) {
            let fullCol = true;
            for (let row = 0; row < gridSize; row++) {
                if (!grid[row][col].filled) {
                    fullCol = false;
                    break;
                }
            }
            if (fullCol) {
                linesToClear.push({ type: 'col', index: col });
            }
        }

        // 清除满行/满列并创建飞行方块
        if (linesToClear.length > 0) {
            linesToClear.forEach(line => {
                if (line.type === 'row') {
                    clearBlockRow.call(this, line.index);
                } else {
                    clearBlockColumn.call(this, line.index);
                }
            });
        }
    }

    // 清除满行
    function clearBlockRow(rowIndex) {
        for (let col = 0; col < gridSize; col++) {
            if (grid[rowIndex][col].sprite) {
                // 创建飞行方块
                createFlyingBlock.call(this,
                    grid[rowIndex][col].sprite.x,
                    grid[rowIndex][col].sprite.y,
                    grid[rowIndex][col].color
                );

                // 添加消除动画
                this.tweens.add({
                    targets: grid[rowIndex][col].sprite,
                    scaleX: 0,
                    scaleY: 0,
                    alpha: 0,
                    duration: 300,
                    ease: 'Power2.easeIn',
                    delay: col * 30,
                    onComplete: () => {
                        if (grid[rowIndex][col].sprite) {
                            grid[rowIndex][col].sprite.destroy();
                            grid[rowIndex][col].sprite = null;
                        }
                    }
                });
            }
            grid[rowIndex][col].filled = false;
            grid[rowIndex][col].color = null;
            blockGridSprites[rowIndex][col] = null;
        }
    }

    // 清除满列
    function clearBlockColumn(colIndex) {
        for (let row = 0; row < gridSize; row++) {
            if (grid[row][colIndex].sprite) {
                // 创建飞行方块
                createFlyingBlock.call(this,
                    grid[row][colIndex].sprite.x,
                    grid[row][colIndex].sprite.y,
                    grid[row][colIndex].color
                );

                // 添加消除动画
                this.tweens.add({
                    targets: grid[row][colIndex].sprite,
                    scaleX: 0,
                    scaleY: 0,
                    alpha: 0,
                    duration: 300,
                    ease: 'Power2.easeIn',
                    delay: row * 30,
                    onComplete: () => {
                        if (grid[row][colIndex].sprite) {
                            grid[row][colIndex].sprite.destroy();
                            grid[row][colIndex].sprite = null;
                        }
                    }
                });
            }
            grid[row][colIndex].filled = false;
            grid[row][colIndex].color = null;
            blockGridSprites[row][colIndex] = null;
        }
    }

    // 创建飞行方块
    function createFlyingBlock(startX, startY, colorInfo) {
        // 检查纹理是否存在
        let textureName = `blockTexture_${colorInfo.name}`;
        if (!this.textures.exists(textureName)) {
            textureName = 'blockTexture'; // 使用备用纹理
        }

        const flyingBlock = this.add.image(startX, startY, textureName);
        flyingBlock.setOrigin(0, 0);
        flyingBlock.setScale(0.8);
        flyingBlock.setDepth(200); // 确保在最上层

        // 如果使用备用纹理，设置对应的颜色
        if (textureName === 'blockTexture') {
            flyingBlock.setTint(colorInfo.color);
        }

        // 存储飞行方块信息
        const flyingBlockData = {
            sprite: flyingBlock,
            startX: startX,
            startY: startY,
            targetX: player.x,
            targetY: player.y - 50, // 飞向主角头部
            progress: 0,
            speed: 0.02, // 飞行速度
            colorInfo: colorInfo
        };

        flyingBlocks.push(flyingBlockData);

        // 添加初始动画效果
        this.tweens.add({
            targets: flyingBlock,
            scaleX: 1.2,
            scaleY: 1.2,
            duration: 200,
            yoyo: true,
            ease: 'Power2'
        });
    }

    // 更新飞行方块
    function updateFlyingBlocks(time, delta) {
        for (let i = flyingBlocks.length - 1; i >= 0; i--) {
            const flyingBlock = flyingBlocks[i];

            // 更新飞行进度
            flyingBlock.progress += flyingBlock.speed;

            if (flyingBlock.progress >= 1) {
                // 到达目标，根据颜色收集方块
                switch (flyingBlock.colorInfo.name) {
                    case 'red':
                        redBlocks++;
                        if (redBlocksText) {
                            redBlocksText.setText(`红色: ${redBlocks}`);
                            this.tweens.add({
                                targets: redBlocksText,
                                scaleX: 1.3,
                                scaleY: 1.3,
                                duration: 200,
                                yoyo: true,
                                ease: 'Power2'
                            });
                        }
                        break;
                    case 'blue':
                        blueBlocks++;
                        if (blueBlocksText) {
                            blueBlocksText.setText(`蓝色: ${blueBlocks}`);
                            this.tweens.add({
                                targets: blueBlocksText,
                                scaleX: 1.3,
                                scaleY: 1.3,
                                duration: 200,
                                yoyo: true,
                                ease: 'Power2'
                            });
                        }
                        break;
                    case 'green':
                        greenBlocks++;
                        if (greenBlocksText) {
                            greenBlocksText.setText(`绿色: ${greenBlocks}`);
                            this.tweens.add({
                                targets: greenBlocksText,
                                scaleX: 1.3,
                                scaleY: 1.3,
                                duration: 200,
                                yoyo: true,
                                ease: 'Power2'
                            });
                        }
                        break;
                }

                // 创建收集特效
                const collectEffect = this.add.circle(player.x, player.y - 50, 15, 0xffdc35);
                this.tweens.add({
                    targets: collectEffect,
                    scaleX: 2,
                    scaleY: 2,
                    alpha: 0,
                    duration: 300,
                    onComplete: () => collectEffect.destroy()
                });

                // 销毁飞行方块
                flyingBlock.sprite.destroy();
                flyingBlocks.splice(i, 1);
            } else {
                // 更新位置（使用贝塞尔曲线创建弧形轨迹）
                const t = flyingBlock.progress;
                const startX = flyingBlock.startX;
                const startY = flyingBlock.startY;
                const targetX = player.x; // 实时更新目标位置
                const targetY = player.y - 50;

                // 计算弧形轨迹的控制点
                const midX = (startX + targetX) / 2;
                const midY = Math.min(startY, targetY) - 100; // 弧形高度

                // 二次贝塞尔曲线
                const currentX = (1 - t) * (1 - t) * startX + 2 * (1 - t) * t * midX + t * t * targetX;
                const currentY = (1 - t) * (1 - t) * startY + 2 * (1 - t) * t * midY + t * t * targetY;

                flyingBlock.sprite.x = currentX;
                flyingBlock.sprite.y = currentY;

                // 添加旋转效果
                flyingBlock.sprite.rotation += 0.1;
            }
        }
    }

    // 开始战斗函数
    function startBattle() {
        if (gameState === 'preparing') {
            gameState = 'fighting';

            // 隐藏战斗按钮
            if (battleButton) {
                battleButton.setVisible(false);
            }

            // 创建怪物
            createWave.call(this);

            // 显示战斗状态提示
            const battleText = this.add.text(375, 150, '战斗开始！', {
                fontSize: '24px',
                fill: '#e74c3c',
                fontFamily: 'Arial',
                fontWeight: 'bold'
            }).setOrigin(0.5);

            this.tweens.add({
                targets: battleText,
                alpha: 0,
                y: battleText.y - 30,
                duration: 2000,
                onComplete: () => battleText.destroy()
            });
        }
    }

    // 计算基于方块数量的攻击力
    function calculateAttackDamage() {
        let baseDamage = 15;
        let totalBlocks = redBlocks + blueBlocks + greenBlocks;

        // 每个方块增加5点攻击力
        let blockBonus = totalBlocks * 5;

        // 颜色组合奖励
        let colorBonus = 0;
        if (redBlocks > 0 && blueBlocks > 0 && greenBlocks > 0) {
            colorBonus = 20; // 三色齐全奖励
        } else if ((redBlocks > 0 && blueBlocks > 0) ||
                   (redBlocks > 0 && greenBlocks > 0) ||
                   (blueBlocks > 0 && greenBlocks > 0)) {
            colorBonus = 10; // 双色奖励
        }

        return baseDamage + blockBonus + colorBonus;
    }

    // 计算攻击次数（基于方块数量）
    function calculateAttackCount() {
        let baseAttacks = 1;
        let totalBlocks = redBlocks + blueBlocks + greenBlocks;

        // 每10个方块增加一次攻击
        let extraAttacks = Math.floor(totalBlocks / 10);

        return Math.min(baseAttacks + extraAttacks, 5); // 最多5次攻击
    }

    // 启动游戏
    window.onload = function() {
        game = new Phaser.Game(config);
    };
</script>

</body>
</html>
