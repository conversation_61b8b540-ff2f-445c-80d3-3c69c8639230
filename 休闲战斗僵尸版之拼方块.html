<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>霰弹枪零件升级战斗</title>
    <script src="https://cdn.jsdelivr.net/npm/phaser@3.80.1/dist/phaser.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background: linear-gradient(135deg, #1e3c72, #2a5298);
            overflow: hidden;
            font-family: Arial, sans-serif;
        }
        canvas {
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            height: 100vh;
        }
    </style>
</head>
<body>

<script>
    // 游戏变量
    let game;
    let player, playerWeapon, monsters = [];
    let playerHealth = 500, maxPlayerHealth = 500;
    let currentLevel = 1, currentWave = 1;
    let gameState = 'playing';
    let monstersKilled = 0;
    let totalMonstersInWave = 8;
    let isCreatingWave = false; // 防止重复创建波次的标志位

    // 方块消除系统变量
    let grid = [];
    let gridSize = 8;
    let cellSize = 35;
    let gridOffsetX = 235; // 居中显示 (750 - 8*35) / 2
    let gridOffsetY = 700;
    let currentBlocks = [];
    let draggedBlock = null;
    let blockPreviewY = 1000;
    let collectedBlocks = 0; // 收集的方块数量
    let collectedBlocksText = null; // 显示收集方块数量的文本
    let flyingBlocks = []; // 飞向主角的方块数组
    let blockGridSprites = []; // 存储网格中的方块精灵

    // 方块形状定义
    const blockShapes = [
        // 单个方块
        [[1]],
        // 直线方块
        [[1, 1]],
        [[1, 1, 1]],
        [[1, 1, 1, 1]],
        [[1], [1]],
        [[1], [1], [1]],
        [[1], [1], [1], [1]],
        // L形方块
        [[1, 0], [1, 1]],
        [[1, 1], [1, 0]],
        [[0, 1], [1, 1]],
        [[1, 0, 0], [1, 1, 1]],
        [[0, 0, 1], [1, 1, 1]],
        [[1, 1, 1], [1, 0, 0]],
        [[1, 1, 1], [0, 0, 1]],
        // 正方形方块
        [[1, 1], [1, 1]],
        [[1, 1, 1], [1, 1, 1], [1, 1, 1]],
        // T形方块
        [[1, 1, 1], [0, 1, 0]],
        [[0, 1], [1, 1], [0, 1]],
        [[0, 1, 0], [1, 1, 1]],
        [[1, 0], [1, 1], [1, 0]],
        // Z形方块
        [[1, 1, 0], [0, 1, 1]],
        [[0, 1, 1], [1, 1, 0]],
        [[1, 0], [1, 1], [0, 1]],
        [[0, 1], [1, 1], [1, 0]]
    ];

    // 玩家属性
    let playerStats = {
        attackDamage: 15,
        attackSpeed: 600,
        maxHealth: 100,
        critChance: 0,
        lifeSteal: 0,
        multiShot: 1
    };

    // 霰弹枪属性系统
    let shotgunStats = {
        fireRate: 600,        // 射击速度 (毫秒间隔)
        stability: 0,         // 稳定性 (减少抖动)
        reloadSpeed: 1000,    // 装填速度
        specialEffect: null,  // 特殊效果 (freeze, burn, explosive)
        effectValue: 0        // 特效强度
    };

    // 已安装的零件
    let installedParts = {
        stock: null,
        magazine: null,
        barrel: null,
        bullet: null
    };

    // 玩家拥有的枪支数量
    let playerGuns = 0;
    // 存储所有玩家武器的数组
    let playerWeapons = [];

    // 游戏配置 - 竖屏优化
    const config = {
        type: Phaser.AUTO,
        width: 750,
        height: 2000, // 增加高度以容纳方块游戏和零件系统
        backgroundColor: '#1a1a2e',
        parent: 'game-container',
        scene: {
            preload: preload,
            create: create,
            update: update
        }
    };

    // 预加载函数
    function preload() {
        // 加载背景图片
        this.load.image('background', 'images/rpg/background8.png');

        // 加载主角图片
        this.load.image('player', 'images/rpg/Character3.png');

        // 加载15个怪物图片
        for (let i = 1; i <= 15; i++) {
            this.load.image(`monster_${i}`, `images/rpg/js (${i}).png`);
        }

        // 加载武器图片
        this.load.image('knife', 'images/knife/kong.png');

        // 加载霰弹枪零件图片
        this.load.image('gun_stock', 'images/gun/枪托.png');
        this.load.image('gun_magazine', 'images/gun/弹夹.png');
        this.load.image('gun_barrel', 'images/gun/枪管.png');

        // 加载子弹图片
        this.load.image('ice_bullet', 'images/gun/冰冻子弹.png');
        this.load.image('fire_bullet', 'images/gun/火焰子弹.png');
        this.load.image('explosive_bullet', 'images/gun/爆炸子弹.png');
    }

    // 创建游戏场景
    function create() {
        // 添加背景图片
        const background = this.add.image(375, 667, 'background');
        background.setDisplaySize(750, 1334); // 适配屏幕大小

        // 创建主角 - 左边位置，向下移动
        player = this.add.image(150, 500, 'player');
        player.setScale(1);
        player.setOrigin(0.5, 1); // 设置旋转中心在底部
        player.setDepth(100 + player.y * 0.1);

        // 创建武器 - 位置在角色右中，与角色重叠
        playerWeapon = this.add.image(player.x + 20, player.y - 30, 'knife');
        playerWeapon.setScale(0.8);
        playerWeapon.setOrigin(0.5, 1);
        playerWeapon.setDepth(100 + player.y * 0.1 + 1);

        // 创建怪物
        createWave.call(this);

        // 创建UI
        createUI.call(this);

        // 创建方块游戏网格
        createBlockGrid.call(this);

        // 创建方块纹理
        createBlockTextures.call(this);

        // 生成初始方块
        generateNewBlocks.call(this);

        // 设置方块游戏输入
        setupBlockInput.call(this);

        // 创建收集方块计数显示
        collectedBlocksText = this.add.text(375, 90, `收集方块: ${collectedBlocks}`, {
            fontSize: '18px',
            fill: '#ffdc35',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5);

        // 添加方块游戏说明
        this.add.text(375, 650, '拼方块游戏 - 消除整行获得方块奖励', {
            fontSize: '16px',
            fill: '#ffffff',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5);

        // 添加方块预览区域说明
        this.add.text(375, 950, '拖拽方块到网格中', {
            fontSize: '14px',
            fill: '#bdc3c7',
            fontFamily: 'Arial'
        }).setOrigin(0.5);

        // 方块预览区域背景
        const blockPreviewBg = this.add.graphics();
        blockPreviewBg.fillStyle(0x34495e, 0.8);
        blockPreviewBg.fillRoundedRect(20, blockPreviewY - 50, 710, 150, 10);
        blockPreviewBg.lineStyle(2, 0x2c3e50);
        blockPreviewBg.strokeRoundedRect(20, blockPreviewY - 50, 710, 150, 10);

        // 创建4x4格子
        createGrid.call(this);
    }

    // 创建波次怪物
    function createWave() {
        isCreatingWave = true;

        // 清除现有怪物和血条
        monsters.forEach(monster => monster.destroy());
        monsters = [];

        if (this.monsterHealthBars) {
            this.monsterHealthBars.forEach(bar => {
                if (bar) bar.destroy();
            });
            this.monsterHealthBars = [];
        }

        // 创建新怪物 - 排成一行，底部与角色对齐
        const monsterSpacing = 80;
        const startX = 450;
        const startY = player.y;

        for (let i = 0; i < totalMonstersInWave; i++) {
            const xPos = startX + i * monsterSpacing;
            const yPos = startY;

            // 随机选择怪物图片 (1-15)
            const monsterImageIndex = Math.floor(Math.random() * 15) + 1;

            let monster = this.add.image(xPos, yPos, `monster_${monsterImageIndex}`);
            monster.setScale(0.5);
            monster.setOrigin(0.5, 1);
            monster.setDepth(100 + yPos * 0.1);

            monster.health = 30 + currentLevel * 10;
            monster.maxHealth = monster.health;
            monster.lastAttack = 0;
            monster.isMoving = false;
            monster.originalX = xPos;
            monster.originalY = yPos;
            monster.jumpTween = null;
            monsters.push(monster);
        }

        monstersKilled = 0;

        setTimeout(() => {
            updateMonsterHealthBars.call(this);
            isCreatingWave = false;
        }, 50);
    }


    // 主角远程攻击系统
    function playerAttack() {
        if (monsters.length > 0) {
            // 计算总攻击次数：基础攻击 + 额外武器攻击
            const totalWeapons = Math.max(1, playerGuns); // 至少有1把武器（原始武器）
            const targetsToAttack = Math.min(playerStats.multiShot * totalWeapons, monsters.length);

            for (let i = 0; i < targetsToAttack; i++) {
                const target = monsters[i % monsters.length]; // 如果武器多于怪物，循环攻击

                // 选择发射武器（轮流使用不同武器）
                let weaponX, weaponY;
                if (i === 0 && playerWeapon) {
                    // 第一发使用原始武器
                    weaponX = playerWeapon.x + 40;
                    weaponY = playerWeapon.y - 50;
                } else if (playerWeapons.length > 0) {
                    // 后续使用额外武器
                    const weaponIndex = (i - 1) % playerWeapons.length;
                    const weapon = playerWeapons[weaponIndex];
                    weaponX = weapon.x + 20;
                    weaponY = weapon.y - 30;
                } else {
                    // 默认位置
                    weaponX = player.x + 60;
                    weaponY = player.y - 30;
                }

                const projectile = this.add.circle(weaponX, weaponY, 4, 0xffff00);

                // 攻击动画
                this.tweens.add({
                    targets: projectile,
                    x: target.x - 30,
                    y: target.y - 100, // 击中怪物中间位置，而不是底部
                    duration: 200, // 从500减少到200，子弹飞行更快
                    delay: i * 50, // 从100减少到50，多重射击间隔更短
                    onComplete: () => {
                        projectile.destroy();

                        // 计算基础伤害
                        let damage = playerStats.attackDamage + currentLevel * 2;

                        // 暴击判定
                        const isCrit = Math.random() < playerStats.critChance;
                        if (isCrit) {
                            damage *= 2;
                            // 暴击特效
                            const critText = this.add.text(target.x, target.y - 80, 'CRIT!', {
                                fontSize: '20px',
                                fill: '#e74c3c',
                                fontFamily: 'Arial',
                                fontWeight: 'bold'
                            }).setOrigin(0.5);

                            this.tweens.add({
                                targets: critText,
                                alpha: 0,
                                y: critText.y - 30,
                                duration: 1000,
                                onComplete: () => critText.destroy()
                            });
                        }

                        // 造成伤害
                        target.health -= damage;

                        // 应用特殊子弹效果
                        applyBulletEffect.call(this, target, damage);

                        // 显示伤害飘字
                        showDamageText.call(this, target, damage);

                        // 怪物受击效果：闪红并停顿
                        monsterHitEffect.call(this, target);

                        // 更新血条
                        updateMonsterHealthBars.call(this);

                        // 生命偷取
                        if (playerStats.lifeSteal > 0) {
                            const healAmount = Math.floor(damage * playerStats.lifeSteal);
                            playerHealth = Math.min(maxPlayerHealth, playerHealth + healAmount);

                            // 治疗特效
                            if (healAmount > 0) {
                                const healText = this.add.text(player.x, player.y - 60, `+${healAmount}`, {
                                    fontSize: '16px',
                                    fill: '#2ecc71',
                                    fontFamily: 'Arial'
                                }).setOrigin(0.5);

                                this.tweens.add({
                                    targets: healText,
                                    alpha: 0,
                                    y: healText.y - 20,
                                    duration: 1000,
                                    onComplete: () => healText.destroy()
                                });
                            }
                        }

                        // 伤害特效 - 减小闪动幅度
                        this.tweens.add({
                            targets: target,
                            scaleX: 0.27, // 轻微放大，避免过大闪动
                            scaleY: 0.27,
                            duration: 150,
                            yoyo: true
                        });

                        // 检查怪物是否死亡
                        if (target.health <= 0) {
                            target.destroy();
                            const index = monsters.indexOf(target);
                            if (index > -1) {
                                monsters.splice(index, 1);
                                monstersKilled++;
                            }
                        }
                    }
                });
            }

            // 主角攻击动画 - 更轻微的抖动
            this.tweens.add({
                targets: player,
                x: player.x + 2, // 减少到2像素，更轻微
                duration: 60,
                yoyo: true,
                ease: 'Power2'
            });

            // 所有武器开火抖动 - 更明显的后坐力
            // 原始武器动画
            if (playerWeapon) {
                animateWeaponRecoil.call(this, playerWeapon);
            }

            // 额外武器动画
            playerWeapons.forEach(weapon => {
                if (weapon) {
                    animateWeaponRecoil.call(this, weapon);
                }
            });
        }
    }

    // 武器后坐力动画
    function animateWeaponRecoil(weapon) {
        const originalWeaponX = weapon.x;
        const originalWeaponY = weapon.y;

        // 水平后坐力
        this.tweens.add({
            targets: weapon,
            x: originalWeaponX - 8, // 向后抖动8像素
            duration: 80,
            ease: 'Power2',
            onComplete: () => {
                // 回到原位
                this.tweens.add({
                    targets: weapon,
                    x: originalWeaponX,
                    duration: 40,
                    ease: 'Power2'
                });
            }
        });

        // 垂直抖动
        this.tweens.add({
            targets: weapon,
            y: originalWeaponY - 4, // 向上抖动4像素
            duration: 80,
            ease: 'Power2',
            onComplete: () => {
                // 回到原位
                this.tweens.add({
                    targets: weapon,
                    y: originalWeaponY,
                    duration: 40,
                    ease: 'Power2'
                });
            }
        });
    }

    // 游戏更新循环
    function update(time, delta) {
        // 更新UI（无论什么状态都要更新）
        updateUI.call(this);

        // 更新血条
        updatePlayerHealthBar.call(this);
        updateEnemyHealthBar.call(this);

        // 更新玩家武器位置
        updatePlayerWeapons.call(this);

        if (gameState === 'playing') {
            // 更新战斗逻辑
            updateBattle.call(this, time, delta);

            // 更新飞行方块
            updateFlyingBlocks.call(this, time, delta);

            // 检查波次完成
            checkWaveComplete.call(this);

            // 检查游戏结束
            if (playerHealth <= 0) {
                gameState = 'gameOver';
                this.add.text(375, 667, 'GAME OVER', {
                    fontSize: '48px',
                    fill: '#e74c3c',
                    fontFamily: 'Arial'
                }).setOrigin(0.5);
            }
        }

        // 更新关卡和波次显示
        if (this.levelText) {
            this.levelText.setText(`关卡 ${currentLevel}`);
        }
        if (this.waveText) {
            this.waveText.setText(`波次 ${currentWave}`);
        }
    }

    // 更新战斗逻辑
    function updateBattle(time, delta) {
        // 主角自动攻击 - 使用动态攻击速度
        if (!this.lastPlayerAttack) this.lastPlayerAttack = 0;
        if (time - this.lastPlayerAttack > playerStats.attackSpeed && monsters.length > 0) {
            playerAttack.call(this);
            this.lastPlayerAttack = time;
        }

        // 怪物AI - 移动到主角身体边并攻击
        monsters.forEach((monster, index) => {
            // 处理冰冻状态
            if (monster.frozen) {
                monster.frozenTime -= delta;
                if (monster.frozenTime <= 0) {
                    monster.frozen = false;
                    monster.clearTint();
                }
                return; // 冰冻时不能行动
            }

            // 处理燃烧状态
            if (monster.burning) {
                monster.burnDuration -= delta;
                if (monster.burnDuration <= 0) {
                    monster.burning = false;
                    monster.clearTint();
                } else {
                    // 每秒造成燃烧伤害
                    if (!monster.lastBurnDamage) monster.lastBurnDamage = 0;
                    if (time - monster.lastBurnDamage > 1000) {
                        monster.health -= monster.burnDamage;
                        monster.lastBurnDamage = time;

                        // 燃烧伤害特效
                        const burnText = this.add.text(monster.x, monster.y - 60, `🔥 -${monster.burnDamage}`, {
                            fontSize: '14px',
                            fill: '#ff4500',
                            fontFamily: 'Arial'
                        }).setOrigin(0.5);

                        this.tweens.add({
                            targets: burnText,
                            alpha: 0,
                            y: burnText.y - 20,
                            duration: 800,
                            onComplete: () => burnText.destroy()
                        });

                        // 检查是否死亡
                        if (monster.health <= 0) {
                            monster.destroy();
                            const monsterIndex = monsters.indexOf(monster);
                            if (monsterIndex > -1) {
                                monsters.splice(monsterIndex, 1);
                                monstersKilled++;
                            }
                            return;
                        }
                    }
                }
            }

            const distanceToPlayer = Phaser.Math.Distance.Between(monster.x, monster.y, player.x, player.y);

            // 如果距离主角太远，移动靠近
            if (distanceToPlayer > 80 && !monster.isMoving) {
                monster.isMoving = true;

                // 计算移动目标位置，避免重叠
                let targetX, targetY;
                let attempts = 0;
                let validPosition = false;

                while (!validPosition && attempts < 10) {
                    // 根据怪物索引分配不同的角度，避免重叠
                    const angle = (index * (Math.PI * 2 / totalMonstersInWave)) + Math.random() * 0.5;
                    const distance =  30+Math.random() * 30;

                    targetX = player.x+80 + Math.cos(angle) * distance;
                    targetY = player.y + Math.sin(angle) * 10; // Y轴范围限制在±20

                    // 检查是否与其他怪物位置冲突
                    validPosition = true;
                    for (let otherMonster of monsters) {
                        if (otherMonster !== monster) {
                            const distanceToOther = Phaser.Math.Distance.Between(targetX, targetY, otherMonster.x, otherMonster.y);
                            if (distanceToOther < 50) { // 最小间距50像素
                                validPosition = false;
                                break;
                            }
                        }
                    }
                    attempts++;
                }

                // 如果找不到合适位置，使用默认位置
                if (!validPosition) {
                    targetX = player.x + 60 + index * 30;
                    targetY = player.y + (index % 2 === 0 ? -15 : 15);
                }

                // 蹦蹦跳跳的移动动画 - 进一步减慢速度
                this.tweens.add({
                    targets: monster,
                    x: targetX,
                    y: targetY,
                    duration: 25000, // 从10000增加到15000，移动更慢
                    ease: 'Power2',
                    onComplete: () => {
                        monster.isMoving = false;
                        // 停止跳跃动画
                        if (monster.jumpTween) {
                            monster.jumpTween.stop();
                            monster.jumpTween = null;
                        }
                        // 移动完成后更新血条位置
                        updateHealthBarPositions.call(this);
                    }
                });

                // 添加跳跃效果 - 上下弹跳
                monster.jumpTween = this.tweens.add({
                    targets: monster,
                    scaleY: 0.52, // 压扁效果（适配新的基础尺寸0.25）
                    duration: 200,
                    yoyo: true,
                    repeat: -1, // 无限重复
                    ease: 'Sine.easeInOut'
                });
            }

            // 如果靠近主角，进行攻击
            if (distanceToPlayer <= 100 && time - monster.lastAttack > 2500) { // 每2.5秒攻击一次
                let damage = 12 + currentLevel;
                playerHealth -= damage;

                monster.lastAttack = time;

                // 停止跳跃动画（如果正在跳跃）
                if (monster.jumpTween) {
                    monster.jumpTween.stop();
                    monster.jumpTween = null;
                }

                // 攻击倾斜动画 - 向主角方向倾斜
                this.tweens.add({
                    targets: monster,
                    rotation: -0.4, // 向左倾斜约23度
                    duration: 150,
                    yoyo: true,
                    ease: 'Power2'
                });

                // 近战攻击动画 - 向主角方向冲刺
                const currentX = monster.x;
                const currentY = monster.y;
                this.tweens.add({
                    targets: monster,
                    x: currentX - 30,
                    y: currentY,
                    duration: 200,
                    yoyo: true,
                    ease: 'Power2',
                    onComplete: () => {
                        // 攻击动画完成后更新血条位置
                        updateHealthBarPositions.call(this);
                    }
                });

                // 攻击时的缩放效果
                this.tweens.add({
                    targets: monster,
                    scaleX: 0.28, // 攻击时稍微放大（适配新的基础尺寸0.25）
                    scaleY: 0.28,
                    duration: 150,
                    yoyo: true
                });

                // 主角受击效果
                this.tweens.add({
                    targets: player,
                    tint: 0xff0000,
                    duration: 100,
                    yoyo: true,
                    onComplete: () => {
                        player.clearTint();
                    }
                });
            }
        });
    }

    // 更新UI
    function updateUI() {
        // 更新血条位置以跟随怪物移动
        updateHealthBarPositions.call(this);
        // 更新怪物深度层级
        updateMonsterDepths.call(this);
    }

    // 更新怪物和主角深度层级，根据Y轴位置
    function updateMonsterDepths() {
        // 更新主角深度
        if (player) {
            player.setDepth(100 + player.y * 0.1);
        }

        // 更新武器深度
        if (playerWeapon) {
            playerWeapon.setDepth(100 + player.y * 0.1 + 1);
        }

        // 更新怪物深度
        monsters.forEach(monster => {
            // Y轴越大（越靠下）深度越大，显示在前面
            // 基础深度100，加上Y轴位置的0.1倍作为偏移
            monster.setDepth(100 + monster.y * 0.1);
        });
    }

    // 更新血条位置以跟随怪物移动
    function updateHealthBarPositions() {
        if (!this.monsterHealthBars) return;

        // 更新武器位置跟随主角
        if (playerWeapon && player) {
            playerWeapon.x = player.x + 20;
            playerWeapon.y = player.y - 30;
        }

        monsters.forEach((monster, index) => {
            if (monster.health > 0) {
                const baseIndex = index * 2; // 每个怪物2个元素：背景、前景

                // 更新血条背景位置和深度
                if (this.monsterHealthBars[baseIndex]) {
                    this.monsterHealthBars[baseIndex].x = monster.x;
                    this.monsterHealthBars[baseIndex].y = monster.y - 140;
                    this.monsterHealthBars[baseIndex].setDepth(100 + monster.y * 0.1 + 50);
                }

                // 更新血条前景位置和深度
                if (this.monsterHealthBars[baseIndex + 1]) {
                    const healthPercent = monster.health / monster.maxHealth;
                    const currentWidth = 48 * healthPercent;
                    this.monsterHealthBars[baseIndex + 1].x = monster.x - 25; // 血条左边缘位置
                    this.monsterHealthBars[baseIndex + 1].y = monster.y - 140;
                    this.monsterHealthBars[baseIndex + 1].width = currentWidth;
                    this.monsterHealthBars[baseIndex + 1].setDepth(100 + monster.y * 0.1 + 51);
                }

                // 移除血量数字位置更新
            }
        });
    }

    // 更新怪物头顶血条 - 优化版本，减少闪动
    function updateMonsterHealthBars() {
        // 初始化血条数组
        if (!this.monsterHealthBars) {
            this.monsterHealthBars = [];
        }

        // 清除多余的血条（当怪物数量减少时）
        while (this.monsterHealthBars.length > monsters.length * 2) {
            const bar = this.monsterHealthBars.pop();
            if (bar) bar.destroy();
        }

        // 为每个怪物更新或创建血条
        monsters.forEach((monster, index) => {
            if (monster.health > 0) {
                const healthPercent = monster.health / monster.maxHealth;
                const baseIndex = index * 2; // 每个怪物2个元素：背景、前景

                // 血条背景
                if (!this.monsterHealthBars[baseIndex]) {
                    const barBg = this.add.rectangle(
                        monster.x,
                        monster.y - 140,
                        50,
                        8,
                        0x2c3e50
                    );
                    barBg.setStrokeStyle(1, 0x000000);
                    // 血条深度比对应怪物高一些，确保显示在怪物上方
                    barBg.setDepth(100 + monster.y * 0.1 + 50);
                    this.monsterHealthBars[baseIndex] = barBg;
                } else {
                    // 更新位置
                    this.monsterHealthBars[baseIndex].x = monster.x;
                    this.monsterHealthBars[baseIndex].y = monster.y - 140;
                }

                // 血条前景
                const currentWidth = 48 * healthPercent;
                if (currentWidth > 0) {
                    // 血条颜色默认为红色
                    let barColor = 0xe74c3c; // 红色

                    if (!this.monsterHealthBars[baseIndex + 1]) {
                        const bar = this.add.rectangle(
                            monster.x - 25, // 血条左边缘位置
                            monster.y - 140,
                            currentWidth,
                            6,
                            barColor
                        );
                        bar.setOrigin(0, 0.5); // 设置原点在左边中间，这样血条从左边开始填充
                        bar.setDepth(100 + monster.y * 0.1 + 51);
                        this.monsterHealthBars[baseIndex + 1] = bar;
                    } else {
                        // 更新血条
                        const bar = this.monsterHealthBars[baseIndex + 1];
                        bar.x = monster.x - 25; // 血条左边缘位置
                        bar.y = monster.y - 140;
                        bar.width = currentWidth;
                        bar.fillColor = barColor;
                        bar.setVisible(true);
                    }
                } else if (this.monsterHealthBars[baseIndex + 1]) {
                    // 血量为0时隐藏血条
                    this.monsterHealthBars[baseIndex + 1].setVisible(false);
                }

                // 移除血量数字显示
            }
        });
    }

    // 检查波次完成
    function checkWaveComplete() {
        // 如果怪物全部死亡且没有正在创建新波次
        if (monsters.length === 0 && !isCreatingWave) {
            isCreatingWave = true;

            currentWave++;

            if (currentWave > 3) {
                currentLevel++;
                currentWave = 1;
                totalMonstersInWave = Math.min(6, 3 + Math.floor(currentLevel / 2));
            }

            // 创建新波次
            setTimeout(() => {
                createWave.call(this);
                isCreatingWave = false; // 创建完成后重置标志位
            }, 1000);
        }
    }

    // 创建UI元素
    function createUI() {
        // 左上角玩家头像
        const playerAvatarBg = this.add.graphics();
        playerAvatarBg.fillStyle(0xffffff); // 蓝色背景
        playerAvatarBg.lineStyle(3, 0xffffff); // 黑色边框
        playerAvatarBg.fillRoundedRect(15, 15, 80, 80, 40); // 圆角矩形
        playerAvatarBg.strokeRoundedRect(15, 15, 80, 80, 40);

        const playerAvatar = this.add.text(55, 55, '🤴', {
            fontSize: '80px'
        }).setOrigin(0.5);

        const playerLabel = this.add.text(105, 20, '玩家10986', {
            fontSize: '20px',
            fill: '#000000',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        });

        // 玩家血条背景
        const playerHealthBarBg = this.add.graphics();
        playerHealthBarBg.fillStyle(0x2c3e50); // 深色背景
        playerHealthBarBg.lineStyle(2, 0x000000); // 黑色边框
        playerHealthBarBg.fillRoundedRect(100, 45, 150, 15, 7); // 圆角矩形
        playerHealthBarBg.strokeRoundedRect(100, 45, 150, 15, 7);

        // 玩家血条
        this.playerHealthBar = this.add.graphics();
        updatePlayerHealthBar.call(this);

        // 玩家血条文字
        this.playerHealthText = this.add.text(175, 52, '', {
            fontSize: '15px',
            fill: '#ffffff',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5);

        // 右上角敌方头像和血条
        const enemyAvatarBg = this.add.graphics();
        enemyAvatarBg.fillStyle(0xe74c3c); // 红色背景
        enemyAvatarBg.lineStyle(3, 0x000000); // 黑色边框
        enemyAvatarBg.fillRoundedRect(655, 15, 80, 80, 40); // 圆角矩形
        enemyAvatarBg.strokeRoundedRect(655, 15, 80, 80, 40);

        const enemyAvatar = this.add.text(695, 60, '🧟', {
            fontSize: '80px'
        }).setOrigin(0.5);

        const enemyLabel = this.add.text(605, 30, '敌方', {
            fontSize: '20px',
            fill: '#000000',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5);

        // 敌方血条背景 - 移到头像右侧同一行
        const healthBarBg = this.add.graphics();
        healthBarBg.fillStyle(0x2c3e50); // 深色背景
        healthBarBg.lineStyle(2, 0x000000); // 黑色边框
        healthBarBg.fillRoundedRect(480, 45, 150, 15, 7); // 圆角矩形，与头像同一行
        healthBarBg.strokeRoundedRect(480, 45, 150, 15, 7);

        // 敌方血条
        this.enemyHealthBar = this.add.graphics();
        updateEnemyHealthBar.call(this);

        // 血条文字
        this.enemyHealthText = this.add.text(555, 52, '', {
            fontSize: '15px',
            fill: '#ffffff',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5);

        // 上方中间关卡和波次信息
        const levelBg = this.add.graphics();
        levelBg.fillStyle(0x34495e, 0.8); // 半透明深色背景
        levelBg.lineStyle(2, 0x000000); // 黑色边框
        levelBg.fillRoundedRect(300, 15, 150, 60, 10); // 圆角矩形
        levelBg.strokeRoundedRect(300, 15, 150, 60, 10);

        this.levelText = this.add.text(375, 35, `关卡 ${currentLevel}`, {
            fontSize: '26px',
            fill: '#ffffff',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5);

        this.waveText = this.add.text(375, 58, `波次 ${currentWave}`, {
            fontSize: '14px',
            fill: '#ecf0f1',
            fontFamily: 'Arial'
        }).setOrigin(0.5);



        // 零件安装提示
        this.add.text(375, 580, '组装完整的枪：枪托+弹夹+枪管+子弹', {
            fontSize: '16px',
            fill: '#ffdc35',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5);

        // 添加列标签
        const cellSize = 100;
        const gridSize = 4;
        const gridWidth = gridSize * cellSize;
        const startX = (750 - gridWidth) / 2 + cellSize / 2;
        const labelY = 590;

        const columnLabels = ['枪托', '弹夹', '枪管', '子弹'];
        for (let col = 0; col < 4; col++) {
            const labelX = startX + col * cellSize;
            this.add.text(labelX, labelY, columnLabels[col], {
                fontSize: '14px',
                fill: '#ffffff',
                fontFamily: 'Arial',
                fontWeight: 'bold'
            }).setOrigin(0.5);
        }
    }

    // 更新玩家血条
    function updatePlayerHealthBar() {
        if (!this.playerHealthBar) return;

        this.playerHealthBar.clear();

        const healthPercent = Math.max(0, playerHealth) / maxPlayerHealth;
        const barWidth = 146; // 血条宽度（减去边框）
        const currentWidth = barWidth * healthPercent;

        // 血条颜色根据血量变化
        let barColor = 0x27ae60; // 绿色
        if (healthPercent < 0.6) barColor = 0xf39c12; // 橙色
        if (healthPercent < 0.3) barColor = 0xe74c3c; // 红色

        this.playerHealthBar.fillStyle(barColor);
        this.playerHealthBar.fillRoundedRect(102, 47, currentWidth, 11, 5); // 圆角血条

        // 更新血条文字
        if (this.playerHealthText) {
            this.playerHealthText.setText(`${Math.max(0, playerHealth)}/${maxPlayerHealth}`);
        }
    }

    // 更新敌方进度条（波次怪物进度）
    function updateEnemyHealthBar() {
        if (!this.enemyHealthBar) return;

        this.enemyHealthBar.clear();

        // 计算波次进度：已击杀的怪物 / 总怪物数
        const totalMonstersInWave = 3 + currentLevel; // 每波怪物总数
        const remainingMonsters = monsters.length; // 剩余怪物数
        const killedMonsters = totalMonstersInWave - remainingMonsters; // 已击杀怪物数

        const progressPercent = killedMonsters / totalMonstersInWave;
        const barWidth = 146; // 进度条宽度（减去边框）
        const currentWidth = barWidth * progressPercent;

        // 进度条颜色根据进度变化
        let barColor = 0xe74c3c; // 红色（开始）
        if (progressPercent > 0.3) barColor = 0xf39c12; // 橙色
        if (progressPercent > 0.6) barColor = 0x27ae60; // 绿色（接近完成）

        this.enemyHealthBar.fillStyle(barColor);
        this.enemyHealthBar.fillRoundedRect(482, 47, currentWidth, 11, 5); // 圆角进度条

        // 更新进度条文字
        if (this.enemyHealthText) {
            this.enemyHealthText.setText(`${killedMonsters}/${totalMonstersInWave}`);
        }
    }
    // 重新排列怪物位置
    function repositionMonsters() {
        const monsterSpacing = 80;
        const startX = 450;
        const startY = player.y; // 与角色底部对齐

        monsters.forEach((monster, index) => {
            const newX = startX + index * monsterSpacing;

            // 平滑移动到新位置
            this.tweens.add({
                targets: monster,
                x: newX,
                y: startY, // 确保Y位置也对齐
                duration: 500,
                ease: 'Power2',
                onComplete: () => {
                    // 移动完成后更新血条位置
                    updateHealthBarPositions.call(this);
                }
            });
        });
    }





    // 怪物受击效果：闪红并停顿
    function monsterHitEffect(monster) {
        // 停止怪物的移动动画
        if (monster.jumpTween) {
            monster.jumpTween.pause();
        }

        // 暂停怪物移动状态
        const wasMoving = monster.isMoving;
        monster.isMoving = false;

        // 闪红效果
        monster.setTint(0xff0000); // 设置红色

        // 轻微震动效果
        const originalX = monster.x;
        const originalY = monster.y;

        this.tweens.add({
            targets: monster,
            x: originalX + 3,
            duration: 50,
            yoyo: true,
            repeat: 2, // 震动3次
            ease: 'Power2',
            onComplete: () => {
                // 恢复原色
                monster.clearTint();

                // 恢复移动状态
                monster.isMoving = wasMoving;
                if (monster.jumpTween && wasMoving) {
                    monster.jumpTween.resume();
                }
            }
        });
    }

    // 显示怪物伤害飘字
    function showDamageText(monster, damage) {
        // 创建伤害文字
        const damageText = this.add.text(
            monster.x + (Math.random() - 0.5) * 20, // 随机偏移位置
            monster.y - 60,
            `-${Math.floor(damage)}`,
            {
                fontSize: '18px',
                fill: '#ff4444',
                fontFamily: 'Arial',
                fontWeight: 'bold',
                stroke: '#000000',
                strokeThickness: 2
            }
        ).setOrigin(0.5);

        // 设置深度确保显示在最上层
        damageText.setDepth(200);

        // 飘字动画
        this.tweens.add({
            targets: damageText,
            y: damageText.y - 40, // 向上飘
            alpha: 0, // 逐渐透明
            scale: 1.2, // 稍微放大
            duration: 800,
            ease: 'Power2',
            onComplete: () => {
                damageText.destroy();
            }
        });
    }

    // 创建4x4战术棋盘
    function createGrid() {
        const cellSize = 100;
        const gridSize = 4;
        const gridWidth = gridSize * cellSize;
        const startX = (750 - gridWidth) / 2 + cellSize / 2;
        const startY = 1200;

        // 初始化棋盘数据
        gameBoard = [];
        tacticalCells = [];
        for (let row = 0; row < gridSize; row++) {
            gameBoard[row] = [];
            tacticalCells[row] = [];
            for (let col = 0; col < gridSize; col++) {
                gameBoard[row][col] = null; // null=空位

                // 创建棋盘格子
                const graphics = this.add.graphics();
                graphics.fillStyle(0x34495e); // 深色背景
                graphics.lineStyle(2, 0x3498db); // 蓝色边框
                graphics.fillRoundedRect(-cellSize/2, -cellSize/2, cellSize, cellSize, 10);
                graphics.strokeRoundedRect(-cellSize/2, -cellSize/2, cellSize, cellSize, 10);

                // 设置位置
                graphics.x = startX + col * cellSize;
                graphics.y = startY + row * cellSize;

                // 添加属性
                graphics.row = row;
                graphics.col = col;
                graphics.piece = null; // 存储棋子对象

                // 设置为可交互（用于拖放检测）
                graphics.setInteractive(new Phaser.Geom.Rectangle(-cellSize/2, -cellSize/2, cellSize, cellSize), Phaser.Geom.Rectangle.Contains);

                tacticalCells[row][col] = graphics;
            }
        }

        // 创建商店
        createShop.call(this);
    }

    // 霰弹枪零件类型定义
    const pieceTypes = {
        STOCK: {
            name: '枪托',
            emoji: '🔧',
            image: 'gun_stock',
            color: 0x8b4513,
            effect: 'stability',
            value: 30,
            description: '固定霰弹枪，减少枪口抖动',
            category: 'stock',
            column: 0  // 第一列
        },

        MAGAZINE: {
            name: '弹夹',
            emoji: '�',
            image: 'gun_magazine',
            color: 0x4682b4,
            effect: 'reloadSpeed',
            value: 40,
            description: '影响弹药装填速度',
            category: 'magazine',
            column: 1  // 第二列
        },
        BARREL: {
            name: '枪管',
            emoji: '🔫',
            image: 'gun_barrel',
            color: 0x2f4f4f,
            effect: 'fireRate',
            value: 35,
            description: '影响射击速度和精准度',
            category: 'barrel',
            column: 2  // 第三列
        },
        ICE_BULLET: {
            name: '冰冻子弹',
            emoji: '❄️',
            image: 'ice_bullet',
            color: 0x87ceeb,
            effect: 'freeze',
            value: 20,
            description: '冰冻特效子弹，减缓敌人速度',
            category: 'bullet',
            column: 3  // 第四列
        },
        FIRE_BULLET: {
            name: '火焰子弹',
            emoji: '�',
            image: 'fire_bullet',
            color: 0xff4500,
            effect: 'burn',
            value: 25,
            description: '火焰特效子弹，持续伤害',
            category: 'bullet',
            column: 3  // 第四列
        },
        EXPLOSIVE_BULLET: {
            name: '爆炸子弹',
            emoji: '�',
            image: 'explosive_bullet',
            color: 0xff6347,
            effect: 'explosive',
            value: 30,
            description: '爆炸特效子弹，范围伤害',
            category: 'bullet',
            column: 3  // 第四列
        }
    };

    // 创建商店
    function createShop() {
        const shopY = 1700;
        const shopStartX = 175; // 调整起始位置，3个棋子居中
        const shopSpacing = 150; // 增加间距

        // 玩家商店背景
        this.shopBackground = this.add.rectangle(375, shopY+30, 600, 250, 0x2c3e50, 0.8);

        // 初始化商店选项
        refreshShop.call(this);
    }

    // 刷新商店
    function refreshShop() {
        // 显示商店背景
        if (this.shopBackground) {
            this.shopBackground.setVisible(true);
        }

        // 清除现有商店物品
        shop.forEach(shopItem => {
            if (shopItem.playerPiece) shopItem.playerPiece.destroy();
            if (shopItem.playerIcon) shopItem.playerIcon.destroy();
            if (shopItem.pieceName) shopItem.pieceName.destroy();
            if (shopItem.pieceDesc) shopItem.pieceDesc.destroy();
        });
        shop = [];

        const shopY = 1630;
        const shopSpacing = 180;
        // 计算居中位置：屏幕中心 - (总宽度/2) + 第一个位置偏移
        const totalWidth = (3 - 1) * shopSpacing; // 2个间距的总宽度
        const shopStartX = 375 - totalWidth / 2; // 375是屏幕中心

        // 随机选择3个棋子类型
        const pieceTypeKeys = Object.keys(pieceTypes);
        const selectedTypes = [];
        while (selectedTypes.length < 3) {
            const randomKey = pieceTypeKeys[Math.floor(Math.random() * pieceTypeKeys.length)];
            if (!selectedTypes.includes(randomKey)) {
                selectedTypes.push(randomKey);
            }
        }

        selectedTypes.forEach((key, index) => {
            const piece = pieceTypes[key];
            const x = shopStartX + index * shopSpacing;

            // 使用图片或emoji，不需要背景
            let playerIcon;
            if (piece.image) {
                playerIcon = this.add.image(x, shopY - 10, piece.image);
                playerIcon.setScale(0.6); // 调整图片大小，稍微大一点
                playerIcon.setOrigin(0.5);
                playerIcon.setInteractive();
                this.input.setDraggable(playerIcon);
            } else {
                playerIcon = this.add.text(x, shopY - 10, piece.emoji, {
                    fontSize: '38px'
                }).setOrigin(0.5);
                playerIcon.setInteractive();
                this.input.setDraggable(playerIcon);
            }

            // 将playerIcon作为主要的可拖拽对象
            const playerPiece = playerIcon;

            // 添加棋子名称 - 更大字体
            const pieceName = this.add.text(x, shopY + 35, piece.name, {
                fontSize: '16px',
                fill: '#ffffff',
                fontFamily: 'Arial',
                fontWeight: 'bold'
            }).setOrigin(0.5);

            // 添加技能描述 - 调整字体和换行
            const pieceDesc = this.add.text(x-60, shopY + 65, piece.description, {
                fontSize: '20px',
                fill: '#ffdc35',
                fontFamily: 'Arial',
                wordWrap: { width: 140, useAdvancedWrap: true },
                align: 'center'
            })

            // 拖动事件
            playerPiece.on('dragstart', () => {
                playerPiece.setScale(1.2);
                playerIcon.setScale(1.2);
                selectedPieceType = key;
            });

            playerPiece.on('drag', (pointer, dragX, dragY) => {
                playerPiece.x = dragX;
                playerPiece.y = dragY;
                playerIcon.x = dragX;
                playerIcon.y = dragY;
            });

            playerPiece.on('dragend', () => {
                // 检查是否拖动到棋盘上
                const cellSize = 100;
                const gridSize = 4;
                const gridWidth = gridSize * cellSize;
                const startX = (750 - gridWidth) / 2 + cellSize / 2;
                const startY = 1200;

                let placed = false;
                for (let row = 0; row < 4; row++) {
                    for (let col = 0; col < 4; col++) {
                        const cellX = startX + col * cellSize;
                        const cellY = startY + row * cellSize;
                        const distance = Phaser.Math.Distance.Between(playerPiece.x, playerPiece.y, cellX, cellY);

                        if (distance < cellSize/2 && gameBoard[row][col] === null) {
                            placeTacticalPiece.call(this, row, col, 'player', key);
                            placed = true;
                            // 下棋成功后刷新商店
                            refreshShop.call(this);
                            break;
                        }
                    }
                    if (placed) break;
                }

                // 重置位置
                playerPiece.x = x;
                playerPiece.y = shopY - 10;
                playerPiece.setScale(1);
                playerIcon.x = x;
                playerIcon.y = shopY - 10;
                playerIcon.setScale(1);
            });

            shop.push({
                playerPiece,
                playerIcon,
                pieceName,
                pieceDesc,
                type: key
            });
        });
    }

    // 安装零件函数
    function placeTacticalPiece(row, col, player, pieceType) {
        if (gameBoard[row][col] !== null) return false;

        const piece = pieceTypes[pieceType];

        // 检查列限制
        if (piece.column !== col) {
            // 显示错误提示
            const errorText = this.add.text(375, 520, `${piece.name}只能放在第${piece.column + 1}列！`, {
                fontSize: '16px',
                fill: '#ff0000',
                fontFamily: 'Arial',
                fontWeight: 'bold'
            }).setOrigin(0.5);

            this.tweens.add({
                targets: errorText,
                alpha: 0,
                duration: 2000,
                onComplete: () => errorText.destroy()
            });
            return false;
        }

        // 创建零件数据
        const pieceData = {
            type: pieceType,
            player: player,
            effect: piece.effect,
            value: piece.value,
            category: piece.category,
            row: row,
            col: col
        };

        // 更新棋盘数据
        gameBoard[row][col] = pieceData;

        // 应用零件效果到霰弹枪
        applyPartEffect(pieceType, piece);

        // 创建零件图形
        const cellSize = 100;
        const gridSize = 4;
        const gridWidth = gridSize * cellSize;
        const startX = (750 - gridWidth) / 2 + cellSize / 2;
        const startY = 1200;

        const pieceX = startX + col * cellSize;
        const pieceY = startY + row * cellSize;

        if (player === 'player') {
            // 零件：直接显示图片或emoji，不需要背景
            let pieceIcon;
            if (piece.image) {
                pieceIcon = this.add.image(pieceX, pieceY, piece.image);
                pieceIcon.setScale(0.4); // 调整图片大小适合格子，稍微大一点
                pieceIcon.setOrigin(0.5);
            } else {
                pieceIcon = this.add.text(pieceX, pieceY, piece.emoji, {
                    fontSize: '20px'
                }).setOrigin(0.5);
            }

            tacticalCells[row][col].piece = { icon: pieceIcon, data: pieceData };
        }

        // 检查是否组成完整的枪
        checkCompleteGun.call(this, row);

        return true;
    }

    // 检查指定行是否组成完整的枪
    function checkCompleteGun(row) {
        // 检查这一行是否有枪托、弹夹、枪管、子弹
        const hasStock = gameBoard[row][0] && gameBoard[row][0].category === 'stock';
        const hasMagazine = gameBoard[row][1] && gameBoard[row][1].category === 'magazine';
        const hasBarrel = gameBoard[row][2] && gameBoard[row][2].category === 'barrel';
        const hasBullet = gameBoard[row][3] && gameBoard[row][3].category === 'bullet';

        if (hasStock && hasMagazine && hasBarrel && hasBullet) {
            // 组成完整的枪！
            playerGuns++;

            // 应用这把枪的效果
            const stockEffect = gameBoard[row][0];
            const magazineEffect = gameBoard[row][1];
            const barrelEffect = gameBoard[row][2];
            const bulletEffect = gameBoard[row][3];

            applyPartEffect(stockEffect.type, pieceTypes[stockEffect.type]);
            applyPartEffect(magazineEffect.type, pieceTypes[magazineEffect.type]);
            applyPartEffect(barrelEffect.type, pieceTypes[barrelEffect.type]);
            applyPartEffect(bulletEffect.type, pieceTypes[bulletEffect.type]);

            // 在主角身上创建额外的武器
            createPlayerWeapon.call(this, playerGuns);

            // 显示获得枪支的提示
            const gunText = this.add.text(375, 400, `🔫 获得新枪支！总计: ${playerGuns}把`, {
                fontSize: '20px',
                fill: '#00ff00',
                fontFamily: 'Arial',
                fontWeight: 'bold'
            }).setOrigin(0.5);

            this.tweens.add({
                targets: gunText,
                alpha: 0,
                y: gunText.y - 50,
                duration: 3000,
                onComplete: () => gunText.destroy()
            });

            // 高亮这一行
            for (let col = 0; col < 4; col++) {
                if (tacticalCells[row][col].piece) {
                    this.tweens.add({
                        targets: tacticalCells[row][col].piece.icon,
                        scaleX: 0.5,
                        scaleY: 0.5,
                        duration: 200,
                        yoyo: true,
                        repeat: 2
                    });
                }
            }
        }
    }

    // 应用零件效果
    function applyPartEffect(pieceType, piece) {
        switch (piece.effect) {
            case 'fireRate':
                // 减少射击间隔，提高射速
                shotgunStats.fireRate = Math.max(200, shotgunStats.fireRate - piece.value);
                playerStats.attackSpeed = shotgunStats.fireRate;
                break;
            case 'stability':
                // 增加稳定性
                shotgunStats.stability += piece.value;
                break;
            case 'reloadSpeed':
                // 减少装填时间
                shotgunStats.reloadSpeed = Math.max(300, shotgunStats.reloadSpeed - piece.value);
                break;
            case 'freeze':
                shotgunStats.specialEffect = 'freeze';
                shotgunStats.effectValue = piece.value;
                break;
            case 'burn':
                shotgunStats.specialEffect = 'burn';
                shotgunStats.effectValue = piece.value;
                break;
            case 'explosive':
                shotgunStats.specialEffect = 'explosive';
                shotgunStats.effectValue = piece.value;
                break;
        }

        // 记录已安装的零件
        switch (piece.category) {
            case 'stock':
                installedParts.stock = pieceType;
                break;
            case 'magazine':
                installedParts.magazine = pieceType;
                break;
            case 'barrel':
                installedParts.barrel = pieceType;
                break;
            case 'bullet':
                installedParts.bullet = pieceType;
                break;
        }
    }



    // 应用特殊子弹效果
    function applyBulletEffect(target, damage) {
        if (!shotgunStats.specialEffect) return;

        switch (shotgunStats.specialEffect) {
            case 'freeze':
                // 冰冻效果
                target.frozen = true;
                target.frozenTime = shotgunStats.effectValue * 100; // 转换为毫秒
                target.setTint(0x87ceeb); // 浅蓝色

                const freezeText = this.add.text(target.x, target.y - 100, '❄️ 冰冻!', {
                    fontSize: '16px',
                    fill: '#87ceeb',
                    fontFamily: 'Arial',
                    fontWeight: 'bold'
                }).setOrigin(0.5);

                this.tweens.add({
                    targets: freezeText,
                    alpha: 0,
                    y: freezeText.y - 20,
                    duration: 1000,
                    onComplete: () => freezeText.destroy()
                });
                break;

            case 'burn':
                // 火焰效果
                if (!target.burning) {
                    target.burning = true;
                    target.burnDamage = Math.floor(damage * 0.3);
                    target.burnDuration = shotgunStats.effectValue * 100;
                    target.setTint(0xff4500); // 橙红色

                    const burnText = this.add.text(target.x, target.y - 100, '🔥 燃烧!', {
                        fontSize: '16px',
                        fill: '#ff4500',
                        fontFamily: 'Arial',
                        fontWeight: 'bold'
                    }).setOrigin(0.5);

                    this.tweens.add({
                        targets: burnText,
                        alpha: 0,
                        y: burnText.y - 20,
                        duration: 1000,
                        onComplete: () => burnText.destroy()
                    });
                }
                break;

            case 'explosive':
                // 爆炸效果
                const explosionRadius = shotgunStats.effectValue * 3;
                const explosionDamage = Math.floor(damage * 0.5);

                // 爆炸特效
                const explosion = this.add.circle(target.x, target.y, explosionRadius, 0xff6347, 0.3);
                this.tweens.add({
                    targets: explosion,
                    scaleX: 1.5,
                    scaleY: 1.5,
                    alpha: 0,
                    duration: 300,
                    onComplete: () => explosion.destroy()
                });

                // 对范围内的其他怪物造成伤害
                monsters.forEach(otherMonster => {
                    if (otherMonster !== target) {
                        const distance = Phaser.Math.Distance.Between(
                            target.x, target.y, otherMonster.x, otherMonster.y
                        );
                        if (distance <= explosionRadius) {
                            otherMonster.health -= explosionDamage;

                            // 爆炸伤害特效
                            this.tweens.add({
                                targets: otherMonster,
                                scaleX: 0.3,
                                scaleY: 0.3,
                                duration: 100,
                                yoyo: true
                            });
                        }
                    }
                });

                const explosiveText = this.add.text(target.x, target.y - 100, '💥 爆炸!', {
                    fontSize: '16px',
                    fill: '#ff6347',
                    fontFamily: 'Arial',
                    fontWeight: 'bold'
                }).setOrigin(0.5);

                this.tweens.add({
                    targets: explosiveText,
                    alpha: 0,
                    y: explosiveText.y - 20,
                    duration: 1000,
                    onComplete: () => explosiveText.destroy()
                });
                break;
        }
    }

    // 创建玩家武器
    function createPlayerWeapon(weaponNumber) {
        // 计算武器位置，围绕主角排列
        const angle = (weaponNumber - 1) * (Math.PI * 2 / 8); // 最多8把武器围成圆圈
        const radius = 40; // 武器距离主角的半径
        const weaponX = player.x + Math.cos(angle) * radius;
        const weaponY = player.y + Math.sin(angle) * radius;

        // 创建武器图像
        const weapon = this.add.image(weaponX, weaponY, 'knife');
        weapon.setScale(0.6);
        weapon.setOrigin(0.5, 1);
        weapon.setDepth(100 + player.y * 0.1 + weaponNumber);

        // 存储武器
        playerWeapons.push(weapon);

        // 武器跟随主角的逻辑会在updatePlayerWeapons中处理
    }

    // 更新所有玩家武器位置
    function updatePlayerWeapons() {
        playerWeapons.forEach((weapon, index) => {
            if (weapon && player) {
                const angle = index * (Math.PI * 2 / Math.max(8, playerWeapons.length));
                const radius = 40;
                weapon.x = player.x + Math.cos(angle) * radius;
                weapon.y = player.y + Math.sin(angle) * radius;
                weapon.setDepth(100 + player.y * 0.1 + index + 1);
            }
        });
    }

    // ========== 方块游戏系统 ==========

    // 创建方块纹理
    function createBlockTextures() {
        const graphics = this.add.graphics();

        // 创建普通方块纹理
        graphics.clear();
        graphics.fillStyle(0x3498db);
        graphics.fillRoundedRect(0, 0, cellSize-2, cellSize-2, 5);
        graphics.lineStyle(2, 0x2980b9);
        graphics.strokeRoundedRect(0, 0, cellSize-2, cellSize-2, 5);
        graphics.generateTexture('blockTexture', cellSize, cellSize);

        // 创建网格背景纹理
        graphics.clear();
        graphics.fillStyle(0x34495e);
        graphics.fillRoundedRect(0, 0, cellSize-1, cellSize-1, 3);
        graphics.lineStyle(1, 0x2c3e50);
        graphics.strokeRoundedRect(0, 0, cellSize-1, cellSize-1, 3);
        graphics.generateTexture('gridCellTexture', cellSize, cellSize);

        // 创建高亮方块纹理
        graphics.clear();
        graphics.fillStyle(0xe74c3c);
        graphics.fillRoundedRect(0, 0, cellSize-2, cellSize-2, 5);
        graphics.lineStyle(2, 0xc0392b);
        graphics.strokeRoundedRect(0, 0, cellSize-2, cellSize-2, 5);
        graphics.generateTexture('highlightBlockTexture', cellSize, cellSize);

        graphics.destroy();
    }

    // 初始化方块游戏网格
    function createBlockGrid() {
        // 初始化游戏网格数据
        grid = [];
        blockGridSprites = [];
        for (let row = 0; row < gridSize; row++) {
            grid[row] = [];
            blockGridSprites[row] = [];
            for (let col = 0; col < gridSize; col++) {
                grid[row][col] = {
                    filled: false,
                    sprite: null,
                    previewSprite: null
                };
                blockGridSprites[row][col] = null;
            }
        }

        // 创建网格视觉效果
        for (let row = 0; row < gridSize; row++) {
            for (let col = 0; col < gridSize; col++) {
                const x = gridOffsetX + col * cellSize;
                const y = gridOffsetY + row * cellSize;
                const cell = this.add.image(x, y, 'gridCellTexture');
                cell.setOrigin(0, 0);
            }
        }
    }

    // 生成新方块
    function generateNewBlocks() {
        // 清除当前方块
        currentBlocks.forEach(blockGroup => {
            if (blockGroup.container) {
                blockGroup.container.destroy();
            }
        });

        currentBlocks = [];

        // 生成3个新方块
        for (let i = 0; i < 3; i++) {
            const shapeIndex = Phaser.Math.Between(0, blockShapes.length - 1);
            const shape = blockShapes[shapeIndex];
            const blockGroup = createBlockGroup.call(this, shape, i);
            currentBlocks.push(blockGroup);
        }
    }

    // 创建方块组
    function createBlockGroup(shape, index) {
        const startX = 125 + index * 200; // 调整起始位置让方块更居中
        const startY = blockPreviewY;

        const container = this.add.container(startX, startY);
        const blocks = [];

        // 创建方块组
        for (let row = 0; row < shape.length; row++) {
            for (let col = 0; col < shape[row].length; col++) {
                if (shape[row][col] === 1) {
                    const block = this.add.image(
                        col * cellSize,
                        row * cellSize,
                        'blockTexture'
                    );
                    block.setOrigin(0, 0);

                    // 给每个方块添加交互事件
                    block.setInteractive();
                    block.blockGroupIndex = index;
                    block.shapeRow = row;
                    block.shapeCol = col;

                    container.add(block);
                    blocks.push(block);
                }
            }
        }

        // 设置容器大小
        container.setSize(shape[0].length * cellSize, shape.length * cellSize);

        return {
            container: container,
            shape: shape,
            blocks: blocks,
            originalX: startX,
            originalY: startY,
            used: false,
            isDragging: false
        };
    }

    // 设置方块游戏输入
    function setupBlockInput() {
        let dragStartPos = { x: 0, y: 0 };
        let dragOffset = { x: 0, y: 0 };

        // 鼠标/触摸按下
        this.input.on('pointerdown', function (pointer, currentlyOver) {
            if (currentlyOver.length > 0) {
                const gameObject = currentlyOver[0];

                // 检查是否点击了方块
                if (gameObject.blockGroupIndex !== undefined) {
                    const blockGroup = currentBlocks[gameObject.blockGroupIndex];

                    if (blockGroup && !blockGroup.used) {
                        // 检查是否在方块预览区域内
                        if (pointer.y >= blockPreviewY - 100 && pointer.y <= blockPreviewY + 150) {
                            draggedBlock = blockGroup;
                            draggedBlock.isDragging = true;

                            // 记录拖拽开始位置和偏移
                            dragStartPos.x = pointer.x;
                            dragStartPos.y = pointer.y;

                            // 计算点击位置相对于容器的偏移
                            dragOffset.x = pointer.x - draggedBlock.container.x;
                            dragOffset.y = pointer.y - draggedBlock.container.y;

                            // 视觉效果
                            draggedBlock.container.setScale(1.1);
                            draggedBlock.container.setDepth(100);
                            draggedBlock.container.setAlpha(0.8);
                        }
                    }
                }
            }
        });

        // 鼠标/触摸移动
        this.input.on('pointermove', function (pointer) {
            if (draggedBlock && draggedBlock.isDragging && !draggedBlock.used) {
                // 更新容器位置
                let newX = pointer.x - dragOffset.x;
                let newY = pointer.y - dragOffset.y;

                draggedBlock.container.x = newX;
                draggedBlock.container.y = newY;

                // 检查是否可以放置并显示预览
                checkBlockPlacementPreview.call(this, newX, newY);
            }
        });

        // 鼠标/触摸释放
        this.input.on('pointerup', function (pointer) {
            if (draggedBlock && draggedBlock.isDragging && !draggedBlock.used) {
                const canPlace = tryPlaceBlockOnGrid.call(this, draggedBlock.container.x, draggedBlock.container.y);

                if (canPlace) {
                    placeBlockOnGrid.call(this);

                    // 保存对当前拖拽方块的引用
                    const currentDraggedBlock = draggedBlock;
                    currentDraggedBlock.used = true;

                    // 放置成功的视觉反馈
                    this.tweens.add({
                        targets: currentDraggedBlock.container,
                        alpha: 0,
                        scaleX: 0.8,
                        scaleY: 0.8,
                        duration: 200,
                        ease: 'Power2.easeIn',
                        onComplete: () => {
                            if (currentDraggedBlock.container) {
                                currentDraggedBlock.container.setVisible(false);
                            }
                        }
                    });

                    // 检查消除
                    setTimeout(() => {
                        checkAndClearBlockLines.call(this);

                        // 检查是否所有方块都已使用
                        if (currentBlocks.every(b => b.used)) {
                            setTimeout(() => {
                                generateNewBlocks.call(this);
                            }, 300);
                        }
                    }, 200);
                } else {
                    // 返回原位置
                    const currentDraggedBlock = draggedBlock;
                    this.tweens.add({
                        targets: currentDraggedBlock.container,
                        x: currentDraggedBlock.originalX,
                        y: currentDraggedBlock.originalY,
                        duration: 300,
                        ease: 'Back.easeOut'
                    });
                }

                // 恢复视觉效果
                if (draggedBlock && draggedBlock.container) {
                    draggedBlock.container.setScale(1);
                    draggedBlock.container.setDepth(0);
                    draggedBlock.container.setAlpha(1);
                    draggedBlock.isDragging = false;
                }
                clearBlockPlacementPreview.call(this);
            }

            draggedBlock = null;
        });
    }

    // 检查方块放置预览
    function checkBlockPlacementPreview(x, y) {
        clearBlockPlacementPreview.call(this);

        if (!draggedBlock) return;

        // 使用容器的左上角位置来计算网格位置
        const gridPos = screenToBlockGrid(x, y);

        if (canPlaceBlockAt(draggedBlock.shape, gridPos.row, gridPos.col)) {
            showBlockPlacementPreview.call(this, draggedBlock.shape, gridPos.row, gridPos.col);
            return true;
        }
        return false;
    }

    // 清除方块放置预览
    function clearBlockPlacementPreview() {
        for (let row = 0; row < gridSize; row++) {
            for (let col = 0; col < gridSize; col++) {
                if (grid[row][col].previewSprite) {
                    grid[row][col].previewSprite.destroy();
                    grid[row][col].previewSprite = null;
                }
            }
        }
    }

    // 显示方块放置预览
    function showBlockPlacementPreview(shape, startRow, startCol) {
        for (let row = 0; row < shape.length; row++) {
            for (let col = 0; col < shape[row].length; col++) {
                if (shape[row][col] === 1) {
                    const gridRow = startRow + row;
                    const gridCol = startCol + col;

                    if (gridRow >= 0 && gridRow < gridSize && gridCol >= 0 && gridCol < gridSize) {
                        const x = gridOffsetX + gridCol * cellSize;
                        const y = gridOffsetY + gridRow * cellSize;

                        const preview = this.add.image(x, y, 'highlightBlockTexture');
                        preview.setOrigin(0, 0);
                        preview.setAlpha(0.6);
                        grid[gridRow][gridCol].previewSprite = preview;
                    }
                }
            }
        }
    }

    // 屏幕坐标转换为网格坐标
    function screenToBlockGrid(screenX, screenY) {
        const col = Math.round((screenX - gridOffsetX) / cellSize);
        const row = Math.round((screenY - gridOffsetY) / cellSize);
        return { row, col };
    }

    // 网格坐标转换为屏幕坐标
    function blockGridToScreen(row, col) {
        return {
            x: gridOffsetX + col * cellSize,
            y: gridOffsetY + row * cellSize
        };
    }

    // 检查是否可以在指定位置放置方块
    function canPlaceBlockAt(shape, startRow, startCol) {
        for (let row = 0; row < shape.length; row++) {
            for (let col = 0; col < shape[row].length; col++) {
                if (shape[row][col] === 1) {
                    const gridRow = startRow + row;
                    const gridCol = startCol + col;

                    // 检查边界
                    if (gridRow < 0 || gridRow >= gridSize || gridCol < 0 || gridCol >= gridSize) {
                        return false;
                    }

                    // 检查是否已被占用
                    if (grid[gridRow][gridCol].filled) {
                        return false;
                    }
                }
            }
        }
        return true;
    }

    // 尝试放置方块
    function tryPlaceBlockOnGrid(screenX, screenY) {
        if (!draggedBlock || !draggedBlock.container) return false;

        const gridPos = screenToBlockGrid(screenX, screenY);

        // 检查是否可以在计算出的网格位置放置方块
        if (canPlaceBlockAt(draggedBlock.shape, gridPos.row, gridPos.col)) {
            // 如果可以放置，将容器位置对齐到网格
            const alignedPos = blockGridToScreen(gridPos.row, gridPos.col);
            draggedBlock.container.x = alignedPos.x;
            draggedBlock.container.y = alignedPos.y;
            return true;
        }

        return false;
    }

    // 在网格上放置方块
    function placeBlockOnGrid() {
        if (!draggedBlock || !draggedBlock.container) return;

        const gridPos = screenToBlockGrid(draggedBlock.container.x, draggedBlock.container.y);
        const shape = draggedBlock.shape;

        // 在网格中放置方块
        for (let row = 0; row < shape.length; row++) {
            for (let col = 0; col < shape[row].length; col++) {
                if (shape[row][col] === 1) {
                    const gridRow = gridPos.row + row;
                    const gridCol = gridPos.col + col;

                    if (gridRow >= 0 && gridRow < gridSize && gridCol >= 0 && gridCol < gridSize) {
                        grid[gridRow][gridCol].filled = true;

                        // 创建方块精灵
                        const x = gridOffsetX + gridCol * cellSize;
                        const y = gridOffsetY + gridRow * cellSize;
                        const blockSprite = this.add.image(x, y, 'blockTexture');
                        blockSprite.setOrigin(0, 0);

                        // 添加放置动画
                        blockSprite.setScale(0);
                        this.tweens.add({
                            targets: blockSprite,
                            scaleX: 1,
                            scaleY: 1,
                            duration: 200,
                            ease: 'Back.easeOut',
                            delay: (row + col) * 50
                        });

                        grid[gridRow][gridCol].sprite = blockSprite;
                        blockGridSprites[gridRow][gridCol] = blockSprite;
                    }
                }
            }
        }
    }

    // 检查并清除满行/满列
    function checkAndClearBlockLines() {
        const linesToClear = [];

        // 检查行
        for (let row = 0; row < gridSize; row++) {
            let fullRow = true;
            for (let col = 0; col < gridSize; col++) {
                if (!grid[row][col].filled) {
                    fullRow = false;
                    break;
                }
            }
            if (fullRow) {
                linesToClear.push({ type: 'row', index: row });
            }
        }

        // 检查列
        for (let col = 0; col < gridSize; col++) {
            let fullCol = true;
            for (let row = 0; row < gridSize; row++) {
                if (!grid[row][col].filled) {
                    fullCol = false;
                    break;
                }
            }
            if (fullCol) {
                linesToClear.push({ type: 'col', index: col });
            }
        }

        // 清除满行/满列并创建飞行方块
        if (linesToClear.length > 0) {
            linesToClear.forEach(line => {
                if (line.type === 'row') {
                    clearBlockRow.call(this, line.index);
                } else {
                    clearBlockColumn.call(this, line.index);
                }
            });
        }
    }

    // 清除满行
    function clearBlockRow(rowIndex) {
        for (let col = 0; col < gridSize; col++) {
            if (grid[rowIndex][col].sprite) {
                // 创建飞行方块
                createFlyingBlock.call(this, grid[rowIndex][col].sprite.x, grid[rowIndex][col].sprite.y);

                // 添加消除动画
                this.tweens.add({
                    targets: grid[rowIndex][col].sprite,
                    scaleX: 0,
                    scaleY: 0,
                    alpha: 0,
                    duration: 300,
                    ease: 'Power2.easeIn',
                    delay: col * 30,
                    onComplete: () => {
                        if (grid[rowIndex][col].sprite) {
                            grid[rowIndex][col].sprite.destroy();
                            grid[rowIndex][col].sprite = null;
                        }
                    }
                });
            }
            grid[rowIndex][col].filled = false;
            blockGridSprites[rowIndex][col] = null;
        }
    }

    // 清除满列
    function clearBlockColumn(colIndex) {
        for (let row = 0; row < gridSize; row++) {
            if (grid[row][colIndex].sprite) {
                // 创建飞行方块
                createFlyingBlock.call(this, grid[row][colIndex].sprite.x, grid[row][colIndex].sprite.y);

                // 添加消除动画
                this.tweens.add({
                    targets: grid[row][colIndex].sprite,
                    scaleX: 0,
                    scaleY: 0,
                    alpha: 0,
                    duration: 300,
                    ease: 'Power2.easeIn',
                    delay: row * 30,
                    onComplete: () => {
                        if (grid[row][colIndex].sprite) {
                            grid[row][colIndex].sprite.destroy();
                            grid[row][colIndex].sprite = null;
                        }
                    }
                });
            }
            grid[row][colIndex].filled = false;
            blockGridSprites[row][colIndex] = null;
        }
    }

    // 创建飞行方块
    function createFlyingBlock(startX, startY) {
        const flyingBlock = this.add.image(startX, startY, 'blockTexture');
        flyingBlock.setOrigin(0, 0);
        flyingBlock.setScale(0.8);
        flyingBlock.setDepth(200); // 确保在最上层

        // 添加发光效果
        flyingBlock.setTint(0xffdc35);

        // 存储飞行方块信息
        const flyingBlockData = {
            sprite: flyingBlock,
            startX: startX,
            startY: startY,
            targetX: player.x,
            targetY: player.y - 50, // 飞向主角头部
            progress: 0,
            speed: 0.02 // 飞行速度
        };

        flyingBlocks.push(flyingBlockData);

        // 添加初始动画效果
        this.tweens.add({
            targets: flyingBlock,
            scaleX: 1.2,
            scaleY: 1.2,
            duration: 200,
            yoyo: true,
            ease: 'Power2'
        });
    }

    // 更新飞行方块
    function updateFlyingBlocks(time, delta) {
        for (let i = flyingBlocks.length - 1; i >= 0; i--) {
            const flyingBlock = flyingBlocks[i];

            // 更新飞行进度
            flyingBlock.progress += flyingBlock.speed;

            if (flyingBlock.progress >= 1) {
                // 到达目标，收集方块
                collectedBlocks++;
                if (collectedBlocksText) {
                    collectedBlocksText.setText(`收集方块: ${collectedBlocks}`);

                    // 添加收集动画
                    this.tweens.add({
                        targets: collectedBlocksText,
                        scaleX: 1.3,
                        scaleY: 1.3,
                        duration: 200,
                        yoyo: true,
                        ease: 'Power2'
                    });
                }

                // 创建收集特效
                const collectEffect = this.add.circle(player.x, player.y - 50, 15, 0xffdc35);
                this.tweens.add({
                    targets: collectEffect,
                    scaleX: 2,
                    scaleY: 2,
                    alpha: 0,
                    duration: 300,
                    onComplete: () => collectEffect.destroy()
                });

                // 销毁飞行方块
                flyingBlock.sprite.destroy();
                flyingBlocks.splice(i, 1);
            } else {
                // 更新位置（使用贝塞尔曲线创建弧形轨迹）
                const t = flyingBlock.progress;
                const startX = flyingBlock.startX;
                const startY = flyingBlock.startY;
                const targetX = player.x; // 实时更新目标位置
                const targetY = player.y - 50;

                // 计算弧形轨迹的控制点
                const midX = (startX + targetX) / 2;
                const midY = Math.min(startY, targetY) - 100; // 弧形高度

                // 二次贝塞尔曲线
                const currentX = (1 - t) * (1 - t) * startX + 2 * (1 - t) * t * midX + t * t * targetX;
                const currentY = (1 - t) * (1 - t) * startY + 2 * (1 - t) * t * midY + t * t * targetY;

                flyingBlock.sprite.x = currentX;
                flyingBlock.sprite.y = currentY;

                // 添加旋转效果
                flyingBlock.sprite.rotation += 0.1;
            }
        }
    }

    // 启动游戏
    window.onload = function() {
        game = new Phaser.Game(config);
    };
</script>

</body>
</html>
